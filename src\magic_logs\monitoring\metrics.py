"""
Prometheus metrics for MagicLogs microservice.
Provides comprehensive monitoring and observability.
"""

from typing import Dict, Any, Optional
import time
from prometheus_client import Counter, Histogram, Gauge, Info, CollectorRegistry, generate_latest
from prometheus_client.core import REGISTRY
import psutil
import asyncio
from datetime import datetime

from ..core.config import get_settings


class PrometheusMetrics:
    """Prometheus metrics collector for MagicLogs."""
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        """Initialize Prometheus metrics."""
        self.registry = registry or REGISTRY
        self.settings = get_settings()
        
        # Request metrics
        self.request_count = Counter(
            'magic_logs_requests_total',
            'Total number of HTTP requests',
            ['method', 'endpoint', 'status_code'],
            registry=self.registry
        )
        
        self.request_duration = Histogram(
            'magic_logs_request_duration_seconds',
            'HTTP request duration in seconds',
            ['method', 'endpoint'],
            registry=self.registry
        )
        
        # Log processing metrics
        self.logs_consumed = Counter(
            'magic_logs_consumed_total',
            'Total number of log messages consumed from Redis',
            ['source_service'],
            registry=self.registry
        )
        
        self.logs_processed = Counter(
            'magic_logs_processed_total',
            'Total number of log messages processed',
            ['source_service', 'status'],
            registry=self.registry
        )
        
        self.logs_written = Counter(
            'magic_logs_written_total',
            'Total number of log messages written to database',
            ['source_service'],
            registry=self.registry
        )
        
        self.log_processing_duration = Histogram(
            'magic_logs_processing_duration_seconds',
            'Log message processing duration in seconds',
            ['source_service'],
            registry=self.registry
        )
        
        self.batch_processing_duration = Histogram(
            'magic_logs_batch_processing_duration_seconds',
            'Batch processing duration in seconds',
            ['batch_size_range'],
            registry=self.registry
        )
        
        # Redis metrics
        self.redis_operations = Counter(
            'magic_logs_redis_operations_total',
            'Total Redis operations',
            ['operation', 'status'],
            registry=self.registry
        )
        
        self.redis_operation_duration = Histogram(
            'magic_logs_redis_operation_duration_seconds',
            'Redis operation duration in seconds',
            ['operation'],
            registry=self.registry
        )
        
        self.redis_stream_lag = Gauge(
            'magic_logs_redis_stream_lag',
            'Redis stream consumer lag',
            ['stream_name', 'consumer_group'],
            registry=self.registry
        )
        
        # Database metrics
        self.db_operations = Counter(
            'magic_logs_db_operations_total',
            'Total database operations',
            ['operation', 'status'],
            registry=self.registry
        )
        
        self.db_operation_duration = Histogram(
            'magic_logs_db_operation_duration_seconds',
            'Database operation duration in seconds',
            ['operation'],
            registry=self.registry
        )
        
        self.db_connection_pool_size = Gauge(
            'magic_logs_db_connection_pool_size',
            'Database connection pool size',
            registry=self.registry
        )
        
        self.db_connection_pool_active = Gauge(
            'magic_logs_db_connection_pool_active',
            'Active database connections in pool',
            registry=self.registry
        )
        
        # Batch processing metrics
        self.batch_size = Histogram(
            'magic_logs_batch_size',
            'Number of messages in processing batch',
            registry=self.registry
        )
        
        self.batches_processed = Counter(
            'magic_logs_batches_processed_total',
            'Total number of batches processed',
            ['status'],
            registry=self.registry
        )
        
        # Partitioning metrics
        self.partitions_created = Counter(
            'magic_logs_partitions_created_total',
            'Total number of database partitions created',
            registry=self.registry
        )
        
        self.partition_maintenance_duration = Histogram(
            'magic_logs_partition_maintenance_duration_seconds',
            'Partition maintenance operation duration',
            registry=self.registry
        )
        
        # System metrics
        self.system_cpu_usage = Gauge(
            'magic_logs_system_cpu_usage_percent',
            'System CPU usage percentage',
            registry=self.registry
        )
        
        self.system_memory_usage = Gauge(
            'magic_logs_system_memory_usage_bytes',
            'System memory usage in bytes',
            registry=self.registry
        )
        
        self.system_memory_available = Gauge(
            'magic_logs_system_memory_available_bytes',
            'System available memory in bytes',
            registry=self.registry
        )
        
        # Application info
        self.app_info = Info(
            'magic_logs_app_info',
            'Application information',
            registry=self.registry
        )
        
        # Error metrics
        self.error_count = Counter(
            'magic_logs_errors_total',
            'Total number of errors',
            ['error_type', 'component'],
            registry=self.registry
        )
        
        # Consumer metrics
        self.consumer_restarts = Counter(
            'magic_logs_consumer_restarts_total',
            'Total number of consumer restarts',
            ['reason'],
            registry=self.registry
        )
        
        self.consumer_uptime = Gauge(
            'magic_logs_consumer_uptime_seconds',
            'Consumer uptime in seconds',
            registry=self.registry
        )
        
        # Initialize app info
        self._set_app_info()
    
    def _set_app_info(self):
        """Set application information metrics."""
        self.app_info.info({
            'version': getattr(self.settings, 'APP_VERSION', '1.0.0'),
            'environment': getattr(self.settings, 'ENVIRONMENT', 'development'),
            'service': 'magic_logs'
        })
    
    def record_request(
        self,
        method: str,
        endpoint: str,
        status_code: int,
        duration: float
    ):
        """Record HTTP request metrics."""
        self.request_count.labels(
            method=method,
            endpoint=endpoint,
            status_code=str(status_code)
        ).inc()
        
        self.request_duration.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
    
    def record_log_consumption(
        self,
        source_service: str,
        count: int = 1
    ):
        """Record log message consumption from Redis."""
        self.logs_consumed.labels(source_service=source_service).inc(count)
    
    def record_log_processing(
        self,
        source_service: str,
        duration: float,
        status: str = 'success'
    ):
        """Record log message processing metrics."""
        self.logs_processed.labels(
            source_service=source_service,
            status=status
        ).inc()
        
        self.log_processing_duration.labels(
            source_service=source_service
        ).observe(duration)
    
    def record_log_write(
        self,
        source_service: str,
        count: int = 1
    ):
        """Record log messages written to database."""
        self.logs_written.labels(source_service=source_service).inc(count)
    
    def record_batch_processing(
        self,
        batch_size: int,
        duration: float,
        status: str = 'success'
    ):
        """Record batch processing metrics."""
        # Categorize batch size
        if batch_size <= 10:
            batch_size_range = 'small'
        elif batch_size <= 100:
            batch_size_range = 'medium'
        elif batch_size <= 1000:
            batch_size_range = 'large'
        else:
            batch_size_range = 'xlarge'
        
        self.batch_size.observe(batch_size)
        self.batches_processed.labels(status=status).inc()
        self.batch_processing_duration.labels(
            batch_size_range=batch_size_range
        ).observe(duration)
    
    def record_redis_operation(
        self,
        operation: str,
        duration: float,
        status: str = 'success'
    ):
        """Record Redis operation metrics."""
        self.redis_operations.labels(
            operation=operation,
            status=status
        ).inc()
        
        self.redis_operation_duration.labels(operation=operation).observe(duration)
    
    def update_redis_stream_lag(
        self,
        stream_name: str,
        consumer_group: str,
        lag: int
    ):
        """Update Redis stream consumer lag."""
        self.redis_stream_lag.labels(
            stream_name=stream_name,
            consumer_group=consumer_group
        ).set(lag)
    
    def record_db_operation(
        self,
        operation: str,
        duration: float,
        status: str = 'success'
    ):
        """Record database operation metrics."""
        self.db_operations.labels(
            operation=operation,
            status=status
        ).inc()
        
        self.db_operation_duration.labels(operation=operation).observe(duration)
    
    def update_connection_pool_metrics(
        self,
        pool_size: int,
        active_connections: int
    ):
        """Update database connection pool metrics."""
        self.db_connection_pool_size.set(pool_size)
        self.db_connection_pool_active.set(active_connections)
    
    def record_partition_creation(self, duration: float):
        """Record partition creation metrics."""
        self.partitions_created.inc()
        self.partition_maintenance_duration.observe(duration)
    
    def record_error(
        self,
        error_type: str,
        component: str = 'unknown'
    ):
        """Record error occurrence."""
        self.error_count.labels(
            error_type=error_type,
            component=component
        ).inc()
    
    def record_consumer_restart(self, reason: str):
        """Record consumer restart."""
        self.consumer_restarts.labels(reason=reason).inc()
    
    def update_consumer_uptime(self, uptime_seconds: float):
        """Update consumer uptime."""
        self.consumer_uptime.set(uptime_seconds)
    
    def update_system_metrics(self):
        """Update system resource metrics."""
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=None)
        self.system_cpu_usage.set(cpu_percent)
        
        # Memory usage
        memory = psutil.virtual_memory()
        self.system_memory_usage.set(memory.used)
        self.system_memory_available.set(memory.available)
    
    def get_metrics(self) -> str:
        """Get metrics in Prometheus format."""
        return generate_latest(self.registry).decode('utf-8')


# Global metrics instance
metrics = PrometheusMetrics()


def get_metrics() -> PrometheusMetrics:
    """Get the global metrics instance."""
    return metrics