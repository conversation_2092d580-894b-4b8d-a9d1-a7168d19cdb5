version: "3.8"

services:
  redis:
    image: redis:7-alpine
    container_name: magic-logs-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    # Optional password support via REDIS_PASSWORD env var
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    command: >
      sh -c 'if [ -n "$REDIS_PASSWORD" ]; then
               echo "Starting Redis with password";
               redis-server --appendonly yes --requirepass "$REDIS_PASSWORD";
             else
               echo "Starting Redis without password";
               redis-server --appendonly yes;
             fi'
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

volumes:
  redis_data:

