"""Unit tests for log database writer."""

import pytest
from unittest.mock import AsyncMock, patch
from datetime import datetime, date

from magic_logs.db.log_writer import LogDatabaseWriter
from magic_logs.core.exceptions import LogProcessingError


@pytest.fixture
def log_writer():
    """Create log writer for testing."""
    return LogDatabaseWriter()


@pytest.fixture
def sample_log_entry():
    """Sample log entry for testing."""
    return {
        'timestamp': '2024-01-15T10:30:00Z',
        'request_id': 'req_123456',
        'correlation_id': 'corr_789',
        'service': 'magic-gateway',
        'level': 'INFO',
        'message': 'Test log message',
        'user_id': '<EMAIL>',
        'endpoint': '/api/v1/test',
        'method': 'GET',
        'status_code': 200,
        'processing_time_ms': 150,
        'metadata': {'key': 'value'}
    }


@pytest.mark.asyncio
async def test_write_log_entry_success(log_writer, sample_log_entry):
    """Test successful single log entry write."""
    with patch('magic_logs.db.log_writer.partition_manager') as mock_partition_manager, \
         patch('magic_logs.db.log_writer.db_manager') as mock_db_manager:
        
        mock_partition_manager.ensure_partition_exists.return_value = True
        mock_conn = AsyncMock()
        mock_db_manager.get_connection.return_value.__aenter__.return_value = mock_conn
        
        result = await log_writer.write_log_entry(sample_log_entry)
        
        assert result is True
        mock_partition_manager.ensure_partition_exists.assert_called_once()
        mock_conn.execute.assert_called_once()


@pytest.mark.asyncio
async def test_write_log_entry_failure(log_writer, sample_log_entry):
    """Test log entry write failure."""
    with patch('magic_logs.db.log_writer.partition_manager') as mock_partition_manager, \
         patch('magic_logs.db.log_writer.db_manager') as mock_db_manager:
        
        mock_partition_manager.ensure_partition_exists.return_value = True
        mock_conn = AsyncMock()
        mock_conn.execute.side_effect = Exception("Database error")
        mock_db_manager.get_connection.return_value.__aenter__.return_value = mock_conn
        
        with pytest.raises(LogProcessingError):
            await log_writer.write_log_entry(sample_log_entry)


@pytest.mark.asyncio
async def test_write_logs_batch_success(log_writer):
    """Test successful batch log write."""
    log_entries = [
        {
            'timestamp': '2024-01-15T10:30:00Z',
            'service': 'test-service',
            'level': 'INFO',
            'message': 'Message 1'
        },
        {
            'timestamp': '2024-01-15T10:31:00Z',
            'service': 'test-service',
            'level': 'WARNING',
            'message': 'Message 2'
        }
    ]
    
    with patch.object(log_writer, '_ensure_partitions_for_batch') as mock_ensure_partitions, \
         patch('magic_logs.db.log_writer.db_manager') as mock_db_manager:
        
        mock_ensure_partitions.return_value = None
        mock_conn = AsyncMock()
        mock_db_manager.get_connection.return_value.__aenter__.return_value = mock_conn
        
        result = await log_writer.write_logs_batch(log_entries)
        
        assert result == 2
        mock_conn.executemany.assert_called_once()


@pytest.mark.asyncio
async def test_write_logs_batch_empty(log_writer):
    """Test batch write with empty list."""
    result = await log_writer.write_logs_batch([])
    assert result == 0


@pytest.mark.asyncio
async def test_write_logs_copy_success(log_writer):
    """Test successful COPY operation."""
    log_entries = [
        {
            'timestamp': '2024-01-15T10:30:00Z',
            'service': 'test-service',
            'level': 'INFO',
            'message': 'Message 1'
        }
    ]
    
    with patch.object(log_writer, '_ensure_partitions_for_batch') as mock_ensure_partitions, \
         patch('magic_logs.db.log_writer.db_manager') as mock_db_manager:
        
        mock_ensure_partitions.return_value = None
        mock_conn = AsyncMock()
        mock_db_manager.get_connection.return_value.__aenter__.return_value = mock_conn
        
        result = await log_writer.write_logs_copy(log_entries)
        
        assert result == 1
        mock_conn.copy_records_to_table.assert_called_once()


@pytest.mark.asyncio
async def test_prepare_log_data_complete(log_writer, sample_log_entry):
    """Test log data preparation with complete entry."""
    log_data = log_writer._prepare_log_data(sample_log_entry)
    
    assert len(log_data) == 14  # All fields
    assert log_data[1] == 'req_123456'  # request_id
    assert log_data[4] == 'INFO'  # level
    assert log_data[5] == 'Test log message'  # message
    assert log_data[9] == 200  # status_code
    assert log_data[10] == 150  # processing_time_ms


@pytest.mark.asyncio
async def test_prepare_log_data_minimal(log_writer):
    """Test log data preparation with minimal entry."""
    minimal_entry = {
        'service': 'test-service',
        'message': 'Test message'
    }
    
    log_data = log_writer._prepare_log_data(minimal_entry)
    
    assert len(log_data) == 14
    assert log_data[3] == 'test-service'  # service
    assert log_data[4] == 'INFO'  # default level
    assert log_data[5] == 'Test message'  # message
    assert isinstance(log_data[0], datetime)  # timestamp should be generated


def test_parse_timestamp_valid(log_writer):
    """Test timestamp parsing with valid formats."""
    # ISO format with Z
    result = log_writer._parse_timestamp('2024-01-15T10:30:00Z')
    assert isinstance(result, datetime)
    
    # ISO format with timezone
    result = log_writer._parse_timestamp('2024-01-15T10:30:00+00:00')
    assert isinstance(result, datetime)


def test_parse_timestamp_invalid(log_writer):
    """Test timestamp parsing with invalid formats."""
    assert log_writer._parse_timestamp('invalid-timestamp') is None
    assert log_writer._parse_timestamp(None) is None
    assert log_writer._parse_timestamp('') is None


def test_safe_int_conversion(log_writer):
    """Test safe integer conversion."""
    assert log_writer._safe_int('123') == 123
    assert log_writer._safe_int(456) == 456
    assert log_writer._safe_int('invalid') is None
    assert log_writer._safe_int(None) is None


@pytest.mark.asyncio
async def test_ensure_partitions_for_batch(log_writer):
    """Test partition creation for batch."""
    log_entries = [
        {'timestamp': '2024-01-15T10:30:00Z'},
        {'timestamp': '2024-01-16T10:30:00Z'},
        {'timestamp': '2024-02-01T10:30:00Z'}
    ]
    
    with patch('magic_logs.db.log_writer.partition_manager') as mock_partition_manager:
        mock_partition_manager.ensure_partition_exists.return_value = True
        
        await log_writer._ensure_partitions_for_batch(log_entries)
        
        # Should be called for each unique date
        assert mock_partition_manager.ensure_partition_exists.call_count >= 2


@pytest.mark.asyncio
async def test_get_log_count_with_date_range(log_writer):
    """Test log count with date range."""
    start_date = date(2024, 1, 1)
    end_date = date(2024, 1, 31)
    
    with patch('magic_logs.db.log_writer.db_manager') as mock_db_manager:
        mock_conn = AsyncMock()
        mock_conn.fetchval.return_value = 150
        mock_db_manager.get_connection.return_value.__aenter__.return_value = mock_conn
        
        result = await log_writer.get_log_count(start_date, end_date)
        
        assert result == 150
        mock_conn.fetchval.assert_called_once()


@pytest.mark.asyncio
async def test_get_log_count_no_date_range(log_writer):
    """Test log count without date range."""
    with patch('magic_logs.db.log_writer.db_manager') as mock_db_manager:
        mock_conn = AsyncMock()
        mock_conn.fetchval.return_value = 500
        mock_db_manager.get_connection.return_value.__aenter__.return_value = mock_conn
        
        result = await log_writer.get_log_count()
        
        assert result == 500


@pytest.mark.asyncio
async def test_cleanup_old_logs_success(log_writer):
    """Test successful old logs cleanup."""
    with patch('magic_logs.db.log_writer.db_manager') as mock_db_manager:
        mock_conn = AsyncMock()
        mock_conn.execute.return_value = "DELETE 25"
        mock_db_manager.get_connection.return_value.__aenter__.return_value = mock_conn
        
        result = await log_writer.cleanup_old_logs(30)
        
        assert result == 25
        mock_conn.execute.assert_called_once()


@pytest.mark.asyncio
async def test_cleanup_old_logs_failure(log_writer):
    """Test old logs cleanup failure."""
    with patch('magic_logs.db.log_writer.db_manager') as mock_db_manager:
        mock_conn = AsyncMock()
        mock_conn.execute.side_effect = Exception("Database error")
        mock_db_manager.get_connection.return_value.__aenter__.return_value = mock_conn
        
        result = await log_writer.cleanup_old_logs(30)
        
        assert result == 0