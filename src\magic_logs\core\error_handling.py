"""
Comprehensive error handling system for MagicLogs.
Provides structured error responses, correlation ID tracking, and error mapping.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
import logging
from enum import Enum
from dataclasses import dataclass, asdict
from fastapi import HTTPException, Request
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware

from .exceptions import (
    MagicLogsException,
    RedisConnectionError,
    DatabaseWriteError,
    LogProcessingError,
    PartitioningError
)
from ..monitoring.correlation import get_correlation_id
from ..monitoring.metrics import get_metrics

logger = logging.getLogger(__name__)


class ErrorSeverity(str, Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(str, Enum):
    """Error categories for classification."""
    REDIS_COMMUNICATION = "redis_communication"
    DATABASE_OPERATION = "database_operation"
    LOG_PROCESSING = "log_processing"
    PARTITIONING = "partitioning"
    VALIDATION = "validation"
    SYSTEM = "system"
    UNKNOWN = "unknown"


@dataclass
class ErrorContext:
    """Context information for errors."""
    correlation_id: Optional[str] = None
    consumer_group: Optional[str] = None
    stream_name: Optional[str] = None
    batch_size: Optional[int] = None
    endpoint: Optional[str] = None
    method: Optional[str] = None
    service: str = "magic_logs"
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow().isoformat()


@dataclass
class StructuredError:
    """Structured error response format."""
    error_code: str
    message: str
    category: ErrorCategory
    severity: ErrorSeverity
    context: ErrorContext
    details: Optional[Dict[str, Any]] = None
    instructions: Optional[List[str]] = None
    retry_after_seconds: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON response."""
        result = {
            "error_code": self.error_code,
            "message": self.message,
            "category": self.category.value,
            "severity": self.severity.value,
            "context": asdict(self.context),
        }
        
        if self.details:
            result["details"] = self.details
        
        if self.instructions:
            result["instructions"] = self.instructions
        
        if self.retry_after_seconds:
            result["retry_after_seconds"] = self.retry_after_seconds
        
        return result


class ErrorMapper:
    """Maps exceptions to structured errors with appropriate HTTP status codes."""
    
    def __init__(self):
        self.error_mappings = {
            # Redis communication errors
            "redis_connection_failed": {
                "status_code": 503,
                "category": ErrorCategory.REDIS_COMMUNICATION,
                "severity": ErrorSeverity.CRITICAL,
                "retry_after_seconds": 30,
                "instructions": [
                    "Check Redis server availability",
                    "Verify network connectivity",
                    "Check Redis authentication credentials"
                ]
            },
            "redis_stream_read_failed": {
                "status_code": 500,
                "category": ErrorCategory.REDIS_COMMUNICATION,
                "severity": ErrorSeverity.HIGH,
                "instructions": [
                    "Check Redis stream configuration",
                    "Verify consumer group settings",
                    "Check Redis server health"
                ]
            },
            "redis_consumer_group_error": {
                "status_code": 500,
                "category": ErrorCategory.REDIS_COMMUNICATION,
                "severity": ErrorSeverity.MEDIUM,
                "instructions": [
                    "Check consumer group configuration",
                    "Verify stream exists",
                    "Check Redis permissions"
                ]
            },
            
            # Database operation errors
            "database_connection_failed": {
                "status_code": 503,
                "category": ErrorCategory.DATABASE_OPERATION,
                "severity": ErrorSeverity.CRITICAL,
                "retry_after_seconds": 30,
                "instructions": [
                    "Check PostgreSQL server availability",
                    "Verify database credentials",
                    "Check PgBouncer configuration"
                ]
            },
            "database_write_failed": {
                "status_code": 500,
                "category": ErrorCategory.DATABASE_OPERATION,
                "severity": ErrorSeverity.HIGH,
                "instructions": [
                    "Check database disk space",
                    "Verify write permissions",
                    "Check table schema compatibility"
                ]
            },
            "connection_pool_exhausted": {
                "status_code": 503,
                "category": ErrorCategory.DATABASE_OPERATION,
                "severity": ErrorSeverity.HIGH,
                "retry_after_seconds": 10,
                "instructions": [
                    "Reduce concurrent operations",
                    "Check connection pool configuration",
                    "Monitor database performance"
                ]
            },
            
            # Log processing errors
            "log_validation_failed": {
                "status_code": 400,
                "category": ErrorCategory.LOG_PROCESSING,
                "severity": ErrorSeverity.LOW,
                "instructions": [
                    "Check log message format",
                    "Verify required fields are present",
                    "Ensure data types are correct"
                ]
            },
            "log_parsing_failed": {
                "status_code": 400,
                "category": ErrorCategory.LOG_PROCESSING,
                "severity": ErrorSeverity.MEDIUM,
                "instructions": [
                    "Check log message structure",
                    "Verify JSON format if applicable",
                    "Check for special characters"
                ]
            },
            "batch_processing_failed": {
                "status_code": 500,
                "category": ErrorCategory.LOG_PROCESSING,
                "severity": ErrorSeverity.HIGH,
                "instructions": [
                    "Check batch size configuration",
                    "Verify memory availability",
                    "Check individual log messages"
                ]
            },
            
            # Partitioning errors
            "partition_creation_failed": {
                "status_code": 500,
                "category": ErrorCategory.PARTITIONING,
                "severity": ErrorSeverity.HIGH,
                "instructions": [
                    "Check database permissions",
                    "Verify partition naming convention",
                    "Check disk space availability"
                ]
            },
            "partition_maintenance_failed": {
                "status_code": 500,
                "category": ErrorCategory.PARTITIONING,
                "severity": ErrorSeverity.MEDIUM,
                "instructions": [
                    "Check partition maintenance schedule",
                    "Verify database performance",
                    "Check for long-running transactions"
                ]
            },
            
            # System errors
            "resource_exhausted": {
                "status_code": 503,
                "category": ErrorCategory.SYSTEM,
                "severity": ErrorSeverity.HIGH,
                "retry_after_seconds": 60,
                "instructions": [
                    "Reduce processing load",
                    "Check system resources",
                    "Scale service if needed"
                ]
            },
            "internal_server_error": {
                "status_code": 500,
                "category": ErrorCategory.SYSTEM,
                "severity": ErrorSeverity.CRITICAL,
                "instructions": [
                    "Check service logs",
                    "Restart service if needed",
                    "Contact support"
                ]
            }
        }
    
    def map_exception_to_error(
        self,
        exception: Exception,
        context: ErrorContext
    ) -> StructuredError:
        """Map an exception to a structured error."""
        error_code = self._get_error_code_from_exception(exception)
        mapping = self.error_mappings.get(error_code, self.error_mappings["internal_server_error"])
        
        # Extract details from exception
        details = self._extract_exception_details(exception)
        
        return StructuredError(
            error_code=error_code,
            message=str(exception),
            category=mapping["category"],
            severity=mapping["severity"],
            context=context,
            details=details,
            instructions=mapping.get("instructions"),
            retry_after_seconds=mapping.get("retry_after_seconds")
        )
    
    def _get_error_code_from_exception(self, exception: Exception) -> str:
        """Get error code from exception type."""
        if isinstance(exception, RedisConnectionError):
            return "redis_connection_failed"
        elif isinstance(exception, DatabaseWriteError):
            if "connection" in str(exception).lower():
                return "database_connection_failed"
            elif "pool" in str(exception).lower():
                return "connection_pool_exhausted"
            else:
                return "database_write_failed"
        elif isinstance(exception, LogProcessingError):
            if "validation" in str(exception).lower():
                return "log_validation_failed"
            elif "parsing" in str(exception).lower():
                return "log_parsing_failed"
            else:
                return "batch_processing_failed"
        elif isinstance(exception, PartitioningError):
            if "creation" in str(exception).lower():
                return "partition_creation_failed"
            else:
                return "partition_maintenance_failed"
        elif isinstance(exception, ConnectionError):
            if "redis" in str(exception).lower():
                return "redis_connection_failed"
            else:
                return "database_connection_failed"
        
        return "internal_server_error"
    
    def _extract_exception_details(self, exception: Exception) -> Dict[str, Any]:
        """Extract additional details from exception."""
        details = {
            "exception_type": type(exception).__name__,
            "exception_module": type(exception).__module__
        }
        
        if isinstance(exception, MagicLogsException):
            if hasattr(exception, 'error_code'):
                details["internal_error_code"] = exception.error_code
            if hasattr(exception, 'context'):
                details["internal_context"] = exception.context
        
        return details
    
    def get_http_status_code(self, error_code: str) -> int:
        """Get HTTP status code for error code."""
        mapping = self.error_mappings.get(error_code, self.error_mappings["internal_server_error"])
        return mapping["status_code"]


class ErrorHandler:
    """Central error handler for the application."""
    
    def __init__(self):
        self.error_mapper = ErrorMapper()
        self.metrics = get_metrics()
    
    def handle_exception(
        self,
        exception: Exception,
        request: Optional[Request] = None
    ) -> JSONResponse:
        """Handle an exception and return structured error response."""
        # Create error context
        context = self._create_error_context(request)
        
        # Map exception to structured error
        structured_error = self.error_mapper.map_exception_to_error(exception, context)
        
        # Log the error
        self._log_error(structured_error, exception)
        
        # Record metrics
        self._record_error_metrics(structured_error)
        
        # Get HTTP status code
        status_code = self.error_mapper.get_http_status_code(structured_error.error_code)
        
        # Create response
        response_data = structured_error.to_dict()
        
        return JSONResponse(
            status_code=status_code,
            content=response_data,
            headers={"X-Correlation-ID": context.correlation_id or "unknown"}
        )
    
    def _create_error_context(self, request: Optional[Request] = None) -> ErrorContext:
        """Create error context from request."""
        correlation_id = get_correlation_id()
        
        context = ErrorContext(
            correlation_id=correlation_id,
            service="magic_logs"
        )
        
        if request:
            context.endpoint = str(request.url.path)
            context.method = request.method
        
        return context
    
    def _log_error(self, structured_error: StructuredError, exception: Exception):
        """Log the structured error."""
        log_data = {
            "error_code": structured_error.error_code,
            "category": structured_error.category.value,
            "severity": structured_error.severity.value,
            "correlation_id": structured_error.context.correlation_id,
            "endpoint": structured_error.context.endpoint,
            "method": structured_error.context.method,
            "exception_type": type(exception).__name__,
            "message": str(exception)
        }
        
        # Log at appropriate level based on severity
        if structured_error.severity == ErrorSeverity.CRITICAL:
            logger.critical("Critical error occurred", extra=log_data, exc_info=True)
        elif structured_error.severity == ErrorSeverity.HIGH:
            logger.error("High severity error occurred", extra=log_data, exc_info=True)
        elif structured_error.severity == ErrorSeverity.MEDIUM:
            logger.warning("Medium severity error occurred", extra=log_data)
        else:
            logger.info("Low severity error occurred", extra=log_data)
    
    def _record_error_metrics(self, structured_error: StructuredError):
        """Record error metrics."""
        self.metrics.record_error(
            error_type=structured_error.error_code,
            component=structured_error.context.endpoint or "unknown"
        )


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware to handle exceptions and return structured error responses."""
    
    def __init__(self, app):
        super().__init__(app)
        self.error_handler = ErrorHandler()
    
    async def dispatch(self, request: Request, call_next):
        """Process request with error handling."""
        try:
            response = await call_next(request)
            return response
        
        except Exception as e:
            # Handle the exception and return structured error response
            return self.error_handler.handle_exception(e, request)


# Global error handler instance
error_handler = ErrorHandler()


def get_error_handler() -> ErrorHandler:
    """Get the global error handler instance."""
    return error_handler