"""Log processor with validation and transformation."""

import json
import time
from datetime import datetime
from typing import List, Dict, Any, Tuple
from pydantic import ValidationError

from loguru import logger

from ..models import LogEntry, ProcessingStats
from ..core.exceptions import LogProcessingError, ValidationError as LogValidationError
from ..db.log_writer import log_writer


class LogProcessor:
    """Processes and validates log messages before database insertion."""
    
    def __init__(self) -> None:
        self.batch_size = 1000
        self.batch_timeout = 5  # seconds
        self.stats = ProcessingStats()
    
    async def process_log_batch(self, messages: List[Dict[str, Any]]) -> ProcessingStats:
        """Process a batch of log messages."""
        start_time = time.time()
        processed_logs = []
        failed_count = 0
        skipped_count = 0
        
        logger.debug(f"Processing batch of {len(messages)} log messages")
        
        for message in messages:
            try:
                log_entry = self._validate_and_transform(message)
                if log_entry:
                    processed_logs.append(log_entry.dict())
                else:
                    skipped_count += 1
            except (ValidationError, LogValidationError) as e:
                logger.warning(f"Invalid log message skipped: {e}")
                failed_count += 1
            except Exception as e:
                logger.error(f"Unexpected error processing log message: {e}")
                failed_count += 1
        
        # Write valid logs to database
        written_count = 0
        if processed_logs:
            try:
                written_count = await log_writer.write_logs_batch(processed_logs)
                logger.debug(f"Successfully wrote {written_count} log entries to database")
            except Exception as e:
                logger.error(f"Failed to write log batch to database: {e}")
                failed_count += len(processed_logs)
                written_count = 0
        
        # Update statistics
        processing_time = int((time.time() - start_time) * 1000)
        self.stats.add_batch_stats(written_count, failed_count, skipped_count, processing_time)
        
        logger.info(
            f"Batch processed: {written_count} written, {failed_count} failed, "
            f"{skipped_count} skipped in {processing_time}ms"
        )
        
        return self.stats
    
    def _validate_and_transform(self, message: Dict[str, Any]) -> LogEntry:
        """Validate and transform a single log message."""
        try:
            # Parse JSON fields if they are strings
            parsed_message = self._parse_redis_message(message)
            
            # Create and validate log entry
            log_entry = LogEntry(**parsed_message)
            
            return log_entry
            
        except ValidationError as e:
            raise LogValidationError(f"Log validation failed: {e}")
        except Exception as e:
            raise LogProcessingError(f"Log transformation failed: {e}")
    
    def _parse_redis_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Redis message fields that may be JSON strings."""
        parsed = {}
        
        for key, value in message.items():
            if isinstance(value, bytes):
                value = value.decode('utf-8')
            
            # Try to parse JSON fields
            if isinstance(value, str) and key in ['metadata']:
                try:
                    parsed[key] = json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    parsed[key] = value
            else:
                parsed[key] = value
        
        return parsed
    
    async def process_single_message(self, message: Dict[str, Any]) -> bool:
        """Process a single log message."""
        try:
            log_entry = self._validate_and_transform(message)
            if log_entry:
                success = await log_writer.write_log_entry(log_entry.dict())
                if success:
                    self.stats.messages_processed += 1
                    return True
                else:
                    self.stats.messages_failed += 1
                    return False
            else:
                self.stats.messages_skipped += 1
                return False
                
        except Exception as e:
            logger.error(f"Failed to process single message: {e}")
            self.stats.messages_failed += 1
            return False
    
    def get_stats(self) -> ProcessingStats:
        """Get current processing statistics."""
        return self.stats
    
    def reset_stats(self) -> None:
        """Reset processing statistics."""
        self.stats = ProcessingStats()
    
    async def validate_message_format(self, message: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate message format without processing."""
        try:
            parsed_message = self._parse_redis_message(message)
            LogEntry(**parsed_message)
            return True, "Valid"
        except ValidationError as e:
            return False, f"Validation error: {e}"
        except Exception as e:
            return False, f"Processing error: {e}"


# Global processor instance
log_processor = LogProcessor()