-- Initialize MagicLogs database

-- Create logs writer user with limited permissions
CREATE USER logs_writer WITH PASSWORD 'logs_password';

-- Create the main logs table (partitioned)
CREATE TABLE IF NOT EXISTS request_logs (
    id BIGSERIAL,
    timestamp TIMESTAMPTZ NOT NULL,
    request_id VARCHAR(255),
    correlation_id VARCHAR(255),
    service VARCHAR(100) NOT NULL,
    level VARCHAR(20) NOT NULL,
    message TEXT,
    user_id VARCHAR(255),
    endpoint VARCHAR(500),
    method VARCHAR(10),
    status_code INTEGER,
    processing_time_ms INTEGER,
    error_type VARCHAR(255),
    error_message TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
) PARTITION BY RANGE (timestamp);

-- <PERSON>reate indexes on the main table
CREATE INDEX IF NOT EXISTS idx_request_logs_timestamp ON request_logs (timestamp);
CREATE INDEX IF NOT EXISTS idx_request_logs_request_id ON request_logs (request_id);
CREATE INDEX IF NOT EXISTS idx_request_logs_service ON request_logs (service);
CREATE INDEX IF NOT EXISTS idx_request_logs_level ON request_logs (level);
CREATE INDEX IF NOT EXISTS idx_request_logs_user_id ON request_logs (user_id);

-- Grant permissions to logs writer
GRANT INSERT, SELECT ON request_logs TO logs_writer;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO logs_writer;

-- Create initial partition for current month
DO $$
DECLARE
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    start_date := date_trunc('month', CURRENT_DATE);
    end_date := start_date + INTERVAL '1 month';
    partition_name := 'request_logs_' || to_char(start_date, 'YYYY_MM');
    
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF request_logs FOR VALUES FROM (%L) TO (%L)',
                   partition_name, start_date, end_date);
    
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_timestamp ON %I (timestamp)', partition_name, partition_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_request_id ON %I (request_id)', partition_name, partition_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_service ON %I (service)', partition_name, partition_name);
    
    -- Grant permissions on partition
    EXECUTE format('GRANT INSERT, SELECT ON %I TO logs_writer', partition_name);
END $$;