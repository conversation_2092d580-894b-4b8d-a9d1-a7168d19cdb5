"""
Exception classes for MagicLogs microservice.
"""

from typing import Dict, Any, Optional


class MagicLogsException(Exception):
    """Base exception for MagicLogs microservice."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.error_code = error_code
        self.context = context or {}


class RedisConnectionError(MagicLogsException):
    """Exception for Redis connection errors."""
    pass


class DatabaseWriteError(MagicLogsException):
    """Exception for database write errors."""
    pass


class LogProcessingError(MagicLogsException):
    """Exception for log processing errors."""
    pass


class PartitioningError(MagicLogsException):
    """Exception for partitioning errors."""
    pass


class ValidationError(MagicLogsException):
    """Exception for validation errors."""
    pass