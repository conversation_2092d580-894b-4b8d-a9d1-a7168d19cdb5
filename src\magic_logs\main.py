"""Main FastAPI application for MagicLogs microservice."""

import asyncio
import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import PlainTextResponse
from loguru import logger

from .core.config import settings
from .core.logging_config import configure_logging
from .core.error_handling import ErrorHandlingMiddleware
from .monitoring.correlation import CorrelationIDMiddleware, CorrelationIDFilter
from .monitoring.metrics import get_metrics
from .api.v1.endpoints import health
from .messaging.redis_client import redis_client
from .db.connection_manager import db_manager
from .db.partitioning import partition_manager
from .processing.consumer import log_consumer


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info(f"Starting {settings.APP_NAME} v{settings.APP_VERSION}")
    
    try:
        # Initialize Redis connection
        await redis_client.initialize()
        logger.info("Redis connection initialized")
        
        # Initialize database connection
        await db_manager.initialize()
        logger.info("Database connection initialized")
        
        # Create base logs table if needed
        await partition_manager.create_base_table_if_not_exists()
        logger.info("Database schema verified")
        
        # Initialize and start log consumer
        await log_consumer.initialize()
        consumer_task = asyncio.create_task(log_consumer.start_consuming())
        logger.info("Log consumer started")
        
        # Store consumer task for cleanup
        app.state.consumer_task = consumer_task
        
        logger.info(f"{settings.APP_NAME} startup completed successfully")
        
    except Exception as e:
        logger.error(f"Failed to start {settings.APP_NAME}: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info(f"Shutting down {settings.APP_NAME}")
    
    try:
        # Stop log consumer
        await log_consumer.stop_consuming()
        if hasattr(app.state, 'consumer_task'):
            app.state.consumer_task.cancel()
            try:
                await app.state.consumer_task
            except asyncio.CancelledError:
                pass
        logger.info("Log consumer stopped")
        
        # Close database connections
        await db_manager.close()
        logger.info("Database connections closed")
        
        # Close Redis connections
        await redis_client.close()
        logger.info("Redis connections closed")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")
    
    logger.info(f"{settings.APP_NAME} shutdown completed")


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    # Configure logging
    configure_logging()
    
    # Add correlation ID filter to logger
    correlation_filter = CorrelationIDFilter()
    logging.getLogger().addFilter(correlation_filter)
    
    # Create FastAPI app
    app = FastAPI(
        title=settings.APP_NAME,
        description="Centralized logging microservice for MagicGateway ecosystem",
        version=settings.APP_VERSION,
        debug=settings.DEBUG,
        lifespan=lifespan
    )
    
    # Add middleware in correct order
    # Error handling middleware should be first
    app.add_middleware(ErrorHandlingMiddleware)
    
    # Correlation ID middleware for distributed tracing
    app.add_middleware(CorrelationIDMiddleware)
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["X-Correlation-ID"],
    )
    
    # Include routers
    app.include_router(
        health.router,
        prefix="/api/v1",
        tags=["health"]
    )
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "service": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "status": "running"
        }
    
    # Add metrics endpoint
    @app.get("/metrics", response_class=PlainTextResponse)
    async def get_prometheus_metrics():
        """Get Prometheus metrics for monitoring."""
        metrics = get_metrics()
        
        # Update system metrics before returning
        metrics.update_system_metrics()
        
        return metrics.get_metrics()
    
    return app


# Create app instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "magic_logs.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )