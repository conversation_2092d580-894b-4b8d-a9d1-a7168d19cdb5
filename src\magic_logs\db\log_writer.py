"""Database writer with batch processing capabilities."""

from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional
import json

from loguru import logger

from .connection_manager import db_manager
from .partitioning import partition_manager
from ..core.exceptions import DatabaseConnectionError, LogProcessingError


class LogDatabaseWriter:
    """Writes logs to PostgreSQL with batch processing and partitioning."""
    
    def __init__(self) -> None:
        self.batch_size = 1000
        self.insert_sql = """
        INSERT INTO request_logs (
            timestamp, request_id, correlation_id, service, level, message,
            user_id, endpoint, method, status_code, processing_time_ms,
            error_type, error_message, metadata
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        """
    
    async def write_log_entry(self, log_entry: Dict[str, Any]) -> bool:
        """Write a single log entry to the database."""
        try:
            # Ensure partition exists for this log entry
            log_timestamp = self._parse_timestamp(log_entry.get('timestamp'))
            if log_timestamp:
                await partition_manager.ensure_partition_exists(log_timestamp.date())
            
            # Prepare log data
            log_data = self._prepare_log_data(log_entry)
            
            async with db_manager.get_connection() as conn:
                await conn.execute(self.insert_sql, *log_data)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to write log entry: {e}")
            raise LogProcessingError(f"Failed to write log entry: {e}")
    
    async def write_logs_batch(self, log_entries: List[Dict[str, Any]]) -> int:
        """Write a batch of logs to the database efficiently."""
        if not log_entries:
            return 0
        
        try:
            # Ensure partitions exist for all log entries
            await self._ensure_partitions_for_batch(log_entries)
            
            # Prepare batch data
            batch_data = []
            for log_entry in log_entries:
                try:
                    log_data = self._prepare_log_data(log_entry)
                    batch_data.append(log_data)
                except Exception as e:
                    logger.warning(f"Skipping invalid log entry: {e}")
                    continue
            
            if not batch_data:
                return 0
            
            # Use executemany for efficient batch insert
            async with db_manager.get_connection() as conn:
                await conn.executemany(self.insert_sql, batch_data)
            
            logger.debug(f"Successfully wrote batch of {len(batch_data)} log entries")
            return len(batch_data)
            
        except Exception as e:
            logger.error(f"Failed to write log batch: {e}")
            raise LogProcessingError(f"Failed to write log batch: {e}")
    
    async def write_logs_copy(self, log_entries: List[Dict[str, Any]]) -> int:
        """Write logs using COPY for maximum performance."""
        if not log_entries:
            return 0
        
        try:
            # Ensure partitions exist
            await self._ensure_partitions_for_batch(log_entries)
            
            # Prepare data for COPY
            records = []
            for log_entry in log_entries:
                try:
                    log_data = self._prepare_log_data(log_entry)
                    records.append(log_data)
                except Exception as e:
                    logger.warning(f"Skipping invalid log entry: {e}")
                    continue
            
            if not records:
                return 0
            
            # Use COPY for bulk insert
            async with db_manager.get_connection() as conn:
                await conn.copy_records_to_table(
                    'request_logs',
                    records=records,
                    columns=[
                        'timestamp', 'request_id', 'correlation_id', 'service', 'level', 'message',
                        'user_id', 'endpoint', 'method', 'status_code', 'processing_time_ms',
                        'error_type', 'error_message', 'metadata'
                    ]
                )
            
            logger.debug(f"Successfully copied {len(records)} log entries")
            return len(records)
            
        except Exception as e:
            logger.error(f"Failed to copy log batch: {e}")
            raise LogProcessingError(f"Failed to copy log batch: {e}")
    
    def _prepare_log_data(self, log_entry: Dict[str, Any]) -> tuple:
        """Prepare log entry data for database insertion."""
        # Parse timestamp
        timestamp = self._parse_timestamp(log_entry.get('timestamp'))
        if not timestamp:
            timestamp = datetime.utcnow()
        
        # Extract and validate fields
        request_id = log_entry.get('request_id')
        correlation_id = log_entry.get('correlation_id')
        service = log_entry.get('service', 'unknown')
        level = log_entry.get('level', 'INFO')
        message = log_entry.get('message', '')
        user_id = log_entry.get('user_id')
        endpoint = log_entry.get('endpoint')
        method = log_entry.get('method')
        
        # Handle numeric fields
        status_code = self._safe_int(log_entry.get('status_code'))
        processing_time_ms = self._safe_int(log_entry.get('processing_time_ms'))
        
        # Handle error fields
        error_type = log_entry.get('error_type')
        error_message = log_entry.get('error_message')
        
        # Handle metadata as JSON
        metadata = log_entry.get('metadata')
        if metadata and not isinstance(metadata, str):
            metadata = json.dumps(metadata)
        
        return (
            timestamp, request_id, correlation_id, service, level, message,
            user_id, endpoint, method, status_code, processing_time_ms,
            error_type, error_message, metadata
        )
    
    def _parse_timestamp(self, timestamp_str: Optional[str]) -> Optional[datetime]:
        """Parse timestamp string to datetime object."""
        if not timestamp_str:
            return None
        
        try:
            # Handle ISO format with timezone
            if timestamp_str.endswith('Z'):
                timestamp_str = timestamp_str[:-1] + '+00:00'
            
            return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        except (ValueError, TypeError):
            logger.warning(f"Invalid timestamp format: {timestamp_str}")
            return None
    
    def _safe_int(self, value: Any) -> Optional[int]:
        """Safely convert value to integer."""
        if value is None:
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            return None
    
    async def _ensure_partitions_for_batch(self, log_entries: List[Dict[str, Any]]) -> None:
        """Ensure all required partitions exist for the batch."""
        dates_needed = set()
        
        for log_entry in log_entries:
            timestamp = self._parse_timestamp(log_entry.get('timestamp'))
            if timestamp:
                dates_needed.add(timestamp.date())
        
        # Ensure partitions exist for all dates
        for target_date in dates_needed:
            await partition_manager.ensure_partition_exists(target_date)
    
    async def get_log_count(self, start_date: Optional[date] = None, end_date: Optional[date] = None) -> int:
        """Get count of logs in specified date range."""
        if start_date and end_date:
            count_sql = """
            SELECT COUNT(*) FROM request_logs 
            WHERE timestamp >= $1 AND timestamp < $2
            """
            params = [start_date, end_date]
        else:
            count_sql = "SELECT COUNT(*) FROM request_logs"
            params = []
        
        try:
            async with db_manager.get_connection() as conn:
                count = await conn.fetchval(count_sql, *params)
                return int(count) if count else 0
        except Exception as e:
            logger.error(f"Failed to get log count: {e}")
            return 0
    
    async def cleanup_old_logs(self, retention_days: int = 90) -> int:
        """Clean up logs older than retention period."""
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        delete_sql = "DELETE FROM request_logs WHERE timestamp < $1"
        
        try:
            async with db_manager.get_connection() as conn:
                result = await conn.execute(delete_sql, cutoff_date)
                # Extract number from result like "DELETE 1234"
                deleted_count = int(result.split()[-1]) if result.split()[-1].isdigit() else 0
                logger.info(f"Cleaned up {deleted_count} old log entries")
                return deleted_count
        except Exception as e:
            logger.error(f"Failed to cleanup old logs: {e}")
            return 0


# Global log writer instance
log_writer = LogDatabaseWriter()