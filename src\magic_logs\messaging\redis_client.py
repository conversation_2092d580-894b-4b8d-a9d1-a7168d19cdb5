"""Redis client wrapper with connection pooling and retry logic."""

import asyncio
from typing import Dict, Any, Optional, List
import uuid
from contextlib import asynccontextmanager

import redis.asyncio as redis
from loguru import logger

from ..core.config import settings
from ..core.exceptions import RedisConnectionError


class RedisClient:
    """Redis client with connection pooling and retry logic."""
    
    def __init__(self) -> None:
        self._pool: Optional[redis.ConnectionPool] = None
        self._client: Optional[redis.Redis] = None
        self._max_retries = settings.MAX_RETRIES
        self._retry_delay = 1.0  # seconds
    
    async def initialize(self) -> None:
        """Initialize Redis connection pool."""
        try:
            self._pool = redis.ConnectionPool.from_url(
                settings.REDIS_URL,
                max_connections=20,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30
            )
            self._client = redis.Redis(connection_pool=self._pool)
            
            # Test connection
            await self._client.ping()
            logger.info("Redis connection initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis connection: {e}")
            raise RedisConnectionError(f"Redis initialization failed: {e}")
    
    async def close(self) -> None:
        """Close Redis connection pool."""
        if self._client:
            await self._client.close()
        if self._pool:
            await self._pool.disconnect()
        logger.info("Redis connection closed")
    
    @asynccontextmanager
    async def get_client(self):
        """Get Redis client with automatic retry logic."""
        if not self._client:
            await self.initialize()
        
        for attempt in range(self._max_retries):
            try:
                yield self._client
                return
            except (redis.ConnectionError, redis.TimeoutError) as e:
                if attempt == self._max_retries - 1:
                    logger.error(f"Redis operation failed after {self._max_retries} attempts: {e}")
                    raise RedisConnectionError(f"Redis operation failed: {e}")
                
                logger.warning(f"Redis operation failed (attempt {attempt + 1}), retrying: {e}")
                await asyncio.sleep(self._retry_delay * (2 ** attempt))  # Exponential backoff
    
    async def xadd(
        self, 
        stream: str, 
        fields: Dict[str, Any], 
        maxlen: Optional[int] = None
    ) -> str:
        """Add message to Redis Stream with retry logic."""
        async with self.get_client() as client:
            return await client.xadd(stream, fields, maxlen=maxlen)
    
    async def xreadgroup(
        self,
        groupname: str,
        consumername: str,
        streams: Dict[str, str],
        count: Optional[int] = None,
        block: Optional[int] = None
    ) -> List[Any]:
        """Read messages from Redis Stream with consumer group."""
        async with self.get_client() as client:
            return await client.xreadgroup(
                groupname, consumername, streams, count=count, block=block
            )
    
    async def xack(self, stream: str, groupname: str, *message_ids: str) -> int:
        """Acknowledge processed messages."""
        async with self.get_client() as client:
            return await client.xack(stream, groupname, *message_ids)
    
    async def xgroup_create(
        self, 
        stream: str, 
        groupname: str, 
        id: str = "0", 
        mkstream: bool = True
    ) -> bool:
        """Create consumer group."""
        async with self.get_client() as client:
            try:
                await client.xgroup_create(stream, groupname, id, mkstream=mkstream)
                return True
            except redis.ResponseError as e:
                if "BUSYGROUP" in str(e):
                    logger.info(f"Consumer group {groupname} already exists")
                    return True
                raise
    
    async def health_check(self) -> bool:
        """Check Redis connection health."""
        try:
            async with self.get_client() as client:
                await client.ping()
                return True
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return False


# Global Redis client instance
redis_client = RedisClient()