@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    MagicLogs Project Startup Script
echo ========================================
echo.

REM Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running or not installed.
    echo Please start Docker Desktop and try again.
    pause
    exit /b 1
)

echo [1/5] Docker is running...

REM Check if .env file exists, if not copy from .env.example
if not exist ".env" (
    echo [2/5] Creating .env file from .env.example...
    copy ".env.example" ".env" >nul
    echo .env file created. You may want to review and modify it.
) else (
    echo [2/5] .env file already exists...
)

REM Stop any existing containers
echo [3/5] Stopping any existing containers...
docker compose -f docker-compose.local.yml down >nul 2>&1

REM Build and start the services
echo [4/5] Building and starting MagicLogs services...
echo This may take a few minutes on first run...
docker compose -f docker-compose.local.yml up -d --build

if %errorlevel% neq 0 (
    echo ERROR: Failed to start services.
    echo Check the output above for details.
    pause
    exit /b 1
)

echo [5/5] Services started successfully!
echo.

REM Wait a moment for services to initialize
echo Waiting for services to initialize...
timeout /t 10 /nobreak >nul

REM Check service health
echo ========================================
echo    Service Health Check
echo ========================================

echo Checking application health...
curl -s http://localhost:8002/api/v1/health/liveness >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Application is responding
) else (
    echo ⚠ Application may still be starting up
)

echo Checking Redis health...
curl -s http://localhost:8002/api/v1/health/redis >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Redis connection is healthy
) else (
    echo ⚠ Redis connection may still be initializing
)

echo Checking database health...
curl -s http://localhost:8002/api/v1/health/database >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Database connection is healthy
) else (
    echo ⚠ Database connection issue - check external PostgreSQL
)

echo.
echo ========================================
echo    MagicLogs is now running!
echo ========================================
echo.
echo Application URL: http://localhost:8002
echo Health Check:   http://localhost:8002/api/v1/health
echo Redis Health:   http://localhost:8002/api/v1/health/redis
echo Database Health: http://localhost:8002/api/v1/health/database
echo.
echo To view logs: docker compose -f docker-compose.local.yml logs -f
echo To stop:      docker compose -f docker-compose.local.yml down
echo.

REM Ask if user wants to open browser
set /p "openBrowser=Open health check in browser? (y/n): "
if /i "!openBrowser!"=="y" (
    start http://localhost:8002/api/v1/health
)

REM Ask if user wants to view logs
set /p "viewLogs=View live logs? (y/n): "
if /i "!viewLogs!"=="y" (
    echo.
    echo Press Ctrl+C to stop viewing logs...
    docker compose -f docker-compose.local.yml logs -f
)

echo.
echo Startup complete! Services are running in the background.
pause
