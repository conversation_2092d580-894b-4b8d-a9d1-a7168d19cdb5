"""Unit tests for Redis client."""

import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import redis.asyncio as redis

from magic_logs.messaging.redis_client import RedisClient
from magic_logs.core.exceptions import RedisConnectionError


@pytest.fixture
def redis_client():
    """Create Redis client for testing."""
    return RedisClient()


@pytest.mark.asyncio
async def test_initialize_success(redis_client):
    """Test successful Redis initialization."""
    with patch('redis.asyncio.ConnectionPool.from_url') as mock_pool, \
         patch('redis.asyncio.Redis') as mock_redis:
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        mock_redis_instance.ping.return_value = True
        
        await redis_client.initialize()
        
        assert redis_client._client is not None
        mock_redis_instance.ping.assert_called_once()


@pytest.mark.asyncio
async def test_initialize_failure(redis_client):
    """Test Redis initialization failure."""
    with patch('redis.asyncio.ConnectionPool.from_url') as mock_pool:
        mock_pool.side_effect = redis.ConnectionError("Connection failed")
        
        with pytest.raises(RedisConnectionError):
            await redis_client.initialize()


@pytest.mark.asyncio
async def test_xadd_success(redis_client):
    """Test successful message addition to stream."""
    with patch.object(redis_client, 'get_client') as mock_get_client:
        mock_client = AsyncMock()
        mock_get_client.return_value.__aenter__.return_value = mock_client
        mock_client.xadd.return_value = "1234567890-0"
        
        result = await redis_client.xadd("test_stream", {"key": "value"})
        
        assert result == "1234567890-0"
        mock_client.xadd.assert_called_once_with("test_stream", {"key": "value"}, maxlen=None)


@pytest.mark.asyncio
async def test_xadd_with_retry(redis_client):
    """Test xadd with retry logic."""
    with patch.object(redis_client, '_client') as mock_client:
        redis_client._client = mock_client
        
        # First call fails, second succeeds
        mock_client.xadd.side_effect = [redis.ConnectionError("Connection lost"), "1234567890-0"]
        
        with patch('asyncio.sleep'):  # Mock sleep to speed up test
            result = await redis_client.xadd("test_stream", {"key": "value"})
        
        assert result == "1234567890-0"
        assert mock_client.xadd.call_count == 2


@pytest.mark.asyncio
async def test_xreadgroup_success(redis_client):
    """Test successful message reading from stream."""
    with patch.object(redis_client, 'get_client') as mock_get_client:
        mock_client = AsyncMock()
        mock_get_client.return_value.__aenter__.return_value = mock_client
        mock_client.xreadgroup.return_value = [["stream", [["1234567890-0", {"key": "value"}]]]]
        
        result = await redis_client.xreadgroup("group", "consumer", {"stream": ">"})
        
        assert len(result) == 1
        mock_client.xreadgroup.assert_called_once()


@pytest.mark.asyncio
async def test_xgroup_create_success(redis_client):
    """Test successful consumer group creation."""
    with patch.object(redis_client, 'get_client') as mock_get_client:
        mock_client = AsyncMock()
        mock_get_client.return_value.__aenter__.return_value = mock_client
        mock_client.xgroup_create.return_value = True
        
        result = await redis_client.xgroup_create("stream", "group")
        
        assert result is True
        mock_client.xgroup_create.assert_called_once_with("stream", "group", "0", mkstream=True)


@pytest.mark.asyncio
async def test_xgroup_create_already_exists(redis_client):
    """Test consumer group creation when group already exists."""
    with patch.object(redis_client, 'get_client') as mock_get_client:
        mock_client = AsyncMock()
        mock_get_client.return_value.__aenter__.return_value = mock_client
        mock_client.xgroup_create.side_effect = redis.ResponseError("BUSYGROUP Consumer Group name already exists")
        
        result = await redis_client.xgroup_create("stream", "group")
        
        assert result is True


@pytest.mark.asyncio
async def test_health_check_success(redis_client):
    """Test successful health check."""
    with patch.object(redis_client, 'get_client') as mock_get_client:
        mock_client = AsyncMock()
        mock_get_client.return_value.__aenter__.return_value = mock_client
        mock_client.ping.return_value = True
        
        result = await redis_client.health_check()
        
        assert result is True


@pytest.mark.asyncio
async def test_health_check_failure(redis_client):
    """Test health check failure."""
    with patch.object(redis_client, 'get_client') as mock_get_client:
        mock_get_client.side_effect = redis.ConnectionError("Connection failed")
        
        result = await redis_client.health_check()
        
        assert result is False


@pytest.mark.asyncio
async def test_close(redis_client):
    """Test connection cleanup."""
    mock_client = AsyncMock()
    mock_pool = AsyncMock()
    
    redis_client._client = mock_client
    redis_client._pool = mock_pool
    
    await redis_client.close()
    
    mock_client.close.assert_called_once()
    mock_pool.disconnect.assert_called_once()