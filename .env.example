# MagicLogs Configuration

# Application settings
APP_NAME=MagicLogs
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO

# Server settings
HOST=0.0.0.0
PORT=8002

# PostgreSQL settings (write-only credentials)
LOGS_DB_HOST=localhost
LOGS_DB_PORT=5432
LOGS_DB_USER=logs_writer
LOGS_DB_PASSWORD=your_password_here
LOGS_DB_NAME=magic_gateway_logs
LOGS_DB_SCHEMA=public

# PgBouncer settings
PGBOUNCER_HOST=localhost
PGBOUNCER_PORT=6432
PGBOUNCER_POOL_SIZE=25
PGBOUNCER_MAX_CLIENT_CONN=100

# Redis settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_STREAM_NAME=logs
REDIS_CONSUMER_GROUP=magic-logs-group

# Processing settings
BATCH_SIZE=1000
BATCH_TIMEOUT_SECONDS=5
MAX_RETRIES=3
PARTITION_RETENTION_MONTHS=12