"""
Correlation ID tracking for distributed request tracing.
"""

import uuid
from typing import <PERSON><PERSON>
from contextvars import Con<PERSON><PERSON><PERSON>
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import logging

# Context variable for correlation ID
_correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)

logger = logging.getLogger(__name__)


class CorrelationIDMiddleware(BaseHTTPMiddleware):
    """Middleware to handle correlation ID for distributed tracing."""
    
    def __init__(self, app, header_name: str = "X-Correlation-ID"):
        super().__init__(app)
        self.header_name = header_name
    
    async def dispatch(self, request: Request, call_next):
        """Process request with correlation ID."""
        # Get correlation ID from header or generate new one
        correlation_id = request.headers.get(self.header_name)
        if not correlation_id:
            correlation_id = str(uuid.uuid4())
        
        # Set correlation ID in context
        token = _correlation_id.set(correlation_id)
        
        try:
            # Add correlation ID to request state for access in endpoints
            request.state.correlation_id = correlation_id
            
            # Process request
            response = await call_next(request)
            
            # Add correlation ID to response headers
            response.headers[self.header_name] = correlation_id
            
            return response
        
        except Exception as e:
            logger.error(
                f"Error processing request with correlation ID {correlation_id}: {e}",
                extra={"correlation_id": correlation_id}
            )
            raise
        
        finally:
            # Reset context
            _correlation_id.reset(token)


def get_correlation_id() -> Optional[str]:
    """Get the current correlation ID from context."""
    return _correlation_id.get()


def set_correlation_id(correlation_id: str) -> None:
    """Set correlation ID in context."""
    _correlation_id.set(correlation_id)


class CorrelationIDFilter(logging.Filter):
    """Logging filter to add correlation ID to log records."""
    
    def filter(self, record):
        """Add correlation ID to log record."""
        correlation_id = get_correlation_id()
        record.correlation_id = correlation_id or "unknown"
        return True