"""Async log publisher for Redis Streams."""

import json
from datetime import datetime
from typing import Dict, Any, List
import uuid

from loguru import logger

from ..core.config import settings
from ..core.exceptions import RedisConnectionError
from .redis_client import redis_client


class AsyncLogPublisher:
    """Publishes log messages to Redis Streams without blocking operations."""
    
    def __init__(self) -> None:
        self.stream_name = settings.REDIS_STREAM_NAME
        self.max_retries = settings.MAX_RETRIES
        self.local_buffer: List[Dict[str, Any]] = []
        self.buffer_size = 100
    
    async def publish_log(self, log_data: Dict[str, Any]) -> None:
        """Publish log message asynchronously."""
        # Add metadata
        enriched_log = {
            **log_data,
            "timestamp": datetime.utcnow().isoformat(),
            "service": settings.APP_NAME,
            "message_id": str(uuid.uuid4())
        }
        
        try:
            # Convert to string format for Redis
            redis_fields = {
                key: json.dumps(value) if isinstance(value, (dict, list)) else str(value)
                for key, value in enriched_log.items()
            }
            
            await redis_client.xadd(
                self.stream_name,
                redis_fields,
                maxlen=1000000  # Keep last 1M messages
            )
            
            logger.debug(f"Published log message to stream {self.stream_name}")
            
        except RedisConnectionError as e:
            # Buffer locally if Redis is unavailable
            self._buffer_locally(enriched_log)
            logger.warning(f"Failed to publish log, buffered locally: {e}")
        except Exception as e:
            logger.error(f"Unexpected error publishing log: {e}")
            self._buffer_locally(enriched_log)
    
    def _buffer_locally(self, log_data: Dict[str, Any]) -> None:
        """Buffer log data locally when Redis is unavailable."""
        self.local_buffer.append(log_data)
        if len(self.local_buffer) > self.buffer_size:
            self.local_buffer.pop(0)  # Remove oldest
        logger.debug(f"Buffered log locally, buffer size: {len(self.local_buffer)}")
    
    async def flush_buffer(self) -> int:
        """Flush local buffer when Redis becomes available."""
        if not self.local_buffer:
            return 0
        
        flushed_count = 0
        buffer_copy = self.local_buffer.copy()
        
        try:
            for log_data in buffer_copy:
                redis_fields = {
                    key: json.dumps(value) if isinstance(value, (dict, list)) else str(value)
                    for key, value in log_data.items()
                }
                
                await redis_client.xadd(self.stream_name, redis_fields)
                flushed_count += 1
            
            # Clear buffer only after successful flush
            self.local_buffer = self.local_buffer[flushed_count:]
            logger.info(f"Flushed {flushed_count} messages from local buffer")
            
        except Exception as e:
            logger.error(f"Failed to flush log buffer: {e}")
        
        return flushed_count
    
    async def get_buffer_size(self) -> int:
        """Get current buffer size."""
        return len(self.local_buffer)
    
    async def publish_batch(self, log_entries: List[Dict[str, Any]]) -> int:
        """Publish multiple log entries efficiently."""
        published_count = 0
        
        for log_data in log_entries:
            try:
                await self.publish_log(log_data)
                published_count += 1
            except Exception as e:
                logger.error(f"Failed to publish log in batch: {e}")
        
        return published_count


# Global publisher instance
log_publisher = AsyncLogPublisher()