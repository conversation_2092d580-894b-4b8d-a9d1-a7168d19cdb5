[tool.poetry]
name = "magic-logs"
version = "1.0.0"
description = "Centralized logging microservice for MagicGateway ecosystem"
authors = ["MagicGateway Team"]
readme = "README.md"
packages = [{include = "magic_logs", from = "src"}]

[tool.poetry.dependencies]
python = "^3.10"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
redis = "^5.0.1"
asyncpg = "^0.29.0"
pydantic = {extras = ["email"], version = "^2.5.0"}
pydantic-settings = "^2.1.0"
loguru = "^0.7.2"
httpx = "^0.25.2"
prometheus-client = "^0.21.0"
psutil = "^6.1.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
black = "^23.11.0"
isort = "^5.12.0"
mypy = "^1.7.1"
types-redis = "^4.6.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
strict = true
warn_return_any = true
warn_unused_configs = true