"""Logging configuration for MagicLogs microservice."""

import sys
from typing import Dict, Any

from loguru import logger

from .config import settings


def configure_logging() -> None:
    """Configure structured logging with loguru."""
    # Remove default handler
    logger.remove()
    
    # Add structured JSON logging
    logger.add(
        sys.stdout,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {message}",
        level=settings.LOG_LEVEL,
        serialize=True,  # JSON format
        enqueue=True,    # Thread-safe
        backtrace=True,
        diagnose=True
    )


def get_log_context(request_id: str, **kwargs: Any) -> Dict[str, Any]:
    """Get structured log context."""
    context = {
        "service": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "request_id": request_id,
    }
    context.update(kwargs)
    return context