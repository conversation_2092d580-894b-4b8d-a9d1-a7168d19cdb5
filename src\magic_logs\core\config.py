"""Configuration settings for MagicLogs microservice."""

from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class LogsSettings(BaseSettings):
    """Configuration for MagicLogs microservice."""
    
    # Application settings
    APP_NAME: str = "MagicLogs"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    
    # Server settings
    HOST: str = "0.0.0.0"
    PORT: int = 8002
    
    # PostgreSQL settings (write-only credentials)
    LOGS_DB_HOST: str = Field(..., description="PostgreSQL host for logs database")
    LOGS_DB_PORT: int = 5432
    LOGS_DB_USER: str = Field(..., description="Write-only database user")
    LOGS_DB_PASSWORD: str = Field(..., description="Database password")
    LOGS_DB_NAME: str = Field(..., description="Database name")
    LOGS_DB_SCHEMA: str = "public"
    
    # PgBouncer settings
    PGBOUNCER_HOST: str = "pgbouncer"
    PGBOUNCER_PORT: int = 6432
    PGBOUNCER_POOL_SIZE: int = 25
    PGBOUNCER_MAX_CLIENT_CONN: int = 100
    
    # Redis settings
    REDIS_HOST: str = "redis"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int = 0
    REDIS_STREAM_NAME: str = "logs"
    REDIS_CONSUMER_GROUP: str = "magic-logs-group"
    
    # Processing settings
    BATCH_SIZE: int = 1000
    BATCH_TIMEOUT_SECONDS: int = 5
    MAX_RETRIES: int = 3
    PARTITION_RETENTION_MONTHS: int = 12
    
    @property
    def LOGS_DB_DSN(self) -> str:
        """Construct logs database DSN."""
        return f"postgresql://{self.LOGS_DB_USER}:{self.LOGS_DB_PASSWORD}@{self.PGBOUNCER_HOST}:{self.PGBOUNCER_PORT}/{self.LOGS_DB_NAME}"
    
    @property
    def REDIS_URL(self) -> str:
        """Construct Redis URL."""
        auth_part = f":{self.REDIS_PASSWORD}@" if self.REDIS_PASSWORD else ""
        return f"redis://{auth_part}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"

    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = LogsSettings()