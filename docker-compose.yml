version: '3.8'

services:
  magic-logs:
    build: .
    ports:
      - "8002:8002"
    environment:
      - LOGS_DB_HOST=postgres
      - PGBOUNCER_HOST=pgbouncer
      - REDIS_HOST=redis
      - LOGS_DB_USER=logs_writer
      - LOGS_DB_PASSWORD=logs_password
      - LOGS_DB_NAME=magic_gateway_logs
    depends_on:
      - postgres
      - pgbouncer
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/api/v1/health/liveness"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=magic_gateway_logs
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped

  pgbouncer:
    image: pgbouncer/pgbouncer:latest
    environment:
      - DATABASES_HOST=postgres
      - DATABASES_PORT=5432
      - DATABASES_USER=logs_writer
      - DATABASES_PASSWORD=logs_password
      - DATABASES_DBNAME=magic_gateway_logs
      - POOL_MODE=transaction
      - MAX_CLIENT_CONN=100
      - DEFAULT_POOL_SIZE=25
    ports:
      - "6432:6432"
    depends_on:
      - postgres
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data: