"""PostgreSQL connection manager with PgBouncer integration."""

import asyncio
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager

import asyncpg
from loguru import logger

from ..core.config import settings
from ..core.exceptions import DatabaseConnectionError


class PostgreSQLConnectionManager:
    """Manages PostgreSQL connections through PgBouncer with connection pooling."""
    
    def __init__(self) -> None:
        self._pool: Optional[asyncpg.Pool] = None
        self._max_retries = settings.MAX_RETRIES
        self._retry_delay = 1.0  # seconds
    
    async def initialize(self) -> None:
        """Initialize PostgreSQL connection pool."""
        try:
            self._pool = await asyncpg.create_pool(
                dsn=settings.LOGS_DB_DSN,
                min_size=5,
                max_size=settings.PGBOUNCER_POOL_SIZE,
                command_timeout=60,
                server_settings={
                    'application_name': f'{settings.APP_NAME}_writer',
                    'search_path': settings.LOGS_DB_SCHEMA
                }
            )
            
            # Test connection
            async with self._pool.acquire() as conn:
                await conn.fetchval('SELECT 1')
            
            logger.info("PostgreSQL connection pool initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL connection pool: {e}")
            raise DatabaseConnectionError(f"PostgreSQL initialization failed: {e}")
    
    async def close(self) -> None:
        """Close PostgreSQL connection pool."""
        if self._pool:
            await self._pool.close()
            logger.info("PostgreSQL connection pool closed")
    
    @asynccontextmanager
    async def get_connection(self):
        """Get database connection with automatic retry logic."""
        if not self._pool:
            await self.initialize()
        
        for attempt in range(self._max_retries):
            try:
                async with self._pool.acquire() as conn:
                    yield conn
                return
            except (asyncpg.PostgresError, asyncpg.InterfaceError) as e:
                if attempt == self._max_retries - 1:
                    logger.error(f"Database operation failed after {self._max_retries} attempts: {e}")
                    raise DatabaseConnectionError(f"Database operation failed: {e}")
                
                logger.warning(f"Database operation failed (attempt {attempt + 1}), retrying: {e}")
                await asyncio.sleep(self._retry_delay * (2 ** attempt))  # Exponential backoff
    
    async def execute_query(self, query: str, *args: Any) -> Any:
        """Execute a query with retry logic."""
        async with self.get_connection() as conn:
            return await conn.execute(query, *args)
    
    async def fetch_one(self, query: str, *args: Any) -> Optional[Dict[str, Any]]:
        """Fetch single row with retry logic."""
        async with self.get_connection() as conn:
            row = await conn.fetchrow(query, *args)
            return dict(row) if row else None
    
    async def fetch_all(self, query: str, *args: Any) -> list[Dict[str, Any]]:
        """Fetch all rows with retry logic."""
        async with self.get_connection() as conn:
            rows = await conn.fetch(query, *args)
            return [dict(row) for row in rows]
    
    async def health_check(self) -> bool:
        """Check database connection health."""
        try:
            async with self.get_connection() as conn:
                await conn.fetchval('SELECT 1')
                return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    async def get_pool_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        if not self._pool:
            return {"status": "not_initialized"}
        
        return {
            "size": self._pool.get_size(),
            "min_size": self._pool.get_min_size(),
            "max_size": self._pool.get_max_size(),
            "idle_size": self._pool.get_idle_size(),
            "status": "healthy" if await self.health_check() else "unhealthy"
        }


# Global connection manager instance
db_manager = PostgreSQLConnectionManager()