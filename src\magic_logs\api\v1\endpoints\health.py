"""Health check endpoints for MagicLogs microservice."""

import time
from datetime import datetime, timedelta
from typing import Dict, Any

from fastapi import APIRouter, HTTPException
from loguru import logger

from ....core.config import settings
from ....models import ServiceHealth
from ....messaging.redis_client import redis_client
from ....db.connection_manager import db_manager
from ....processing.consumer import log_consumer
from ....processing.log_processor import log_processor

router = APIRouter()

# Track service start time
_service_start_time = time.time()


@router.get("/health", response_model=ServiceHealth)
async def health_check() -> ServiceHealth:
    """Get comprehensive service health status."""
    try:
        # Check Redis connection
        redis_healthy = await redis_client.health_check()
        
        # Check database connection
        db_healthy = await db_manager.health_check()
        
        # Get consumer status
        consumer_status = log_consumer.get_status()
        consumer_running = consumer_status.get('running', False)
        
        # Get processing statistics
        processing_stats = log_processor.get_stats()
        
        # Get buffer size from publisher
        from ....messaging.publisher import log_publisher
        buffer_size = await log_publisher.get_buffer_size()
        
        # Determine overall health status
        if redis_healthy and db_healthy and consumer_running:
            status = "healthy"
        elif redis_healthy or db_healthy:
            status = "degraded"
        else:
            status = "unhealthy"
        
        # Calculate uptime
        uptime_seconds = int(time.time() - _service_start_time)
        
        return ServiceHealth(
            status=status,
            version=settings.APP_VERSION,
            uptime_seconds=uptime_seconds,
            redis_connected=redis_healthy,
            database_connected=db_healthy,
            consumer_running=consumer_running,
            messages_in_buffer=buffer_size,
            processing_stats=processing_stats
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {e}")


@router.get("/health/liveness")
async def liveness_probe() -> Dict[str, str]:
    """Kubernetes liveness probe endpoint."""
    return {"status": "alive", "timestamp": datetime.utcnow().isoformat()}


@router.get("/health/readiness")
async def readiness_probe() -> Dict[str, Any]:
    """Kubernetes readiness probe endpoint."""
    try:
        # Check if service is ready to accept traffic
        redis_healthy = await redis_client.health_check()
        db_healthy = await db_manager.health_check()
        
        if redis_healthy and db_healthy:
            return {
                "status": "ready",
                "timestamp": datetime.utcnow().isoformat(),
                "redis": "connected",
                "database": "connected"
            }
        else:
            raise HTTPException(
                status_code=503,
                detail={
                    "status": "not_ready",
                    "redis": "connected" if redis_healthy else "disconnected",
                    "database": "connected" if db_healthy else "disconnected"
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Readiness check failed: {e}")


@router.get("/health/redis")
async def redis_health() -> Dict[str, Any]:
    """Detailed Redis health information."""
    try:
        is_healthy = await redis_client.health_check()
        
        if is_healthy:
            return {
                "status": "healthy",
                "connected": True,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {
                "status": "unhealthy",
                "connected": False,
                "timestamp": datetime.utcnow().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        return {
            "status": "error",
            "connected": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/health/database")
async def database_health() -> Dict[str, Any]:
    """Detailed database health information."""
    try:
        is_healthy = await db_manager.health_check()
        pool_stats = await db_manager.get_pool_stats()
        
        return {
            "status": "healthy" if is_healthy else "unhealthy",
            "connected": is_healthy,
            "pool_stats": pool_stats,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "error",
            "connected": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/health/consumer")
async def consumer_health() -> Dict[str, Any]:
    """Detailed consumer health information."""
    try:
        consumer_status = log_consumer.get_status()
        processing_stats = log_processor.get_stats()
        
        return {
            "status": "healthy" if consumer_status.get('running') else "stopped",
            "consumer_info": consumer_status,
            "processing_stats": processing_stats.dict(),
            "dead_letter_queue_size": log_consumer.get_dead_letter_queue_size(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Consumer health check failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/metrics")
async def get_metrics() -> Dict[str, Any]:
    """Get service metrics for monitoring."""
    try:
        # Get processing statistics
        processing_stats = log_processor.get_stats()
        
        # Get consumer status
        consumer_status = log_consumer.get_status()
        
        # Get database pool stats
        pool_stats = await db_manager.get_pool_stats()
        
        # Get buffer size
        from ....messaging.publisher import log_publisher
        buffer_size = await log_publisher.get_buffer_size()
        
        # Calculate uptime
        uptime_seconds = int(time.time() - _service_start_time)
        
        return {
            "service": {
                "name": settings.APP_NAME,
                "version": settings.APP_VERSION,
                "uptime_seconds": uptime_seconds
            },
            "processing": processing_stats.dict(),
            "consumer": {
                "running": consumer_status.get('running', False),
                "dead_letter_queue_size": consumer_status.get('dead_letter_queue_size', 0)
            },
            "database": pool_stats,
            "buffer": {
                "size": buffer_size,
                "max_size": log_publisher.buffer_size
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {e}")


@router.post("/health/reset-stats")
async def reset_processing_stats() -> Dict[str, str]:
    """Reset processing statistics (for testing/debugging)."""
    try:
        log_processor.reset_stats()
        return {
            "status": "success",
            "message": "Processing statistics reset",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to reset stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to reset stats: {e}")