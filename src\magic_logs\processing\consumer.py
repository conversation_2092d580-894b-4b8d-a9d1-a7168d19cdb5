"""Redis Streams consumer with consumer group management."""

import asyncio
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime

from loguru import logger

from ..core.config import settings
from ..core.exceptions import RedisConnectionError, LogProcessingError
from ..messaging.redis_client import redis_client
from .log_processor import log_processor


class LogConsumerService:
    """Consumes log messages from Redis Streams and processes them."""
    
    def __init__(self) -> None:
        self.consumer_group = settings.REDIS_CONSUMER_GROUP
        self.consumer_name = f"consumer-{uuid.uuid4().hex[:8]}"
        self.stream_name = settings.REDIS_STREAM_NAME
        self.batch_size = settings.BATCH_SIZE
        self.batch_timeout = settings.BATCH_TIMEOUT_SECONDS * 1000  # Convert to milliseconds
        self.max_retries = settings.MAX_RETRIES
        self.running = False
        self.dead_letter_queue: List[Dict[str, Any]] = []
        self.max_dlq_size = 1000
    
    async def initialize(self) -> None:
        """Initialize consumer group and Redis connection."""
        try:
            # Ensure Redis connection is initialized
            await redis_client.initialize()
            
            # Create consumer group if it doesn't exist
            await redis_client.xgroup_create(
                self.stream_name, 
                self.consumer_group, 
                id="0", 
                mkstream=True
            )
            
            logger.info(
                f"Consumer initialized: group={self.consumer_group}, "
                f"consumer={self.consumer_name}, stream={self.stream_name}"
            )
            
        except Exception as e:
            logger.error(f"Failed to initialize consumer: {e}")
            raise RedisConnectionError(f"Consumer initialization failed: {e}")
    
    async def start_consuming(self) -> None:
        """Start consuming log messages from Redis Streams."""
        if self.running:
            logger.warning("Consumer is already running")
            return
        
        self.running = True
        logger.info(f"Starting log consumer: {self.consumer_name}")
        
        try:
            while self.running:
                try:
                    # Read messages from stream
                    messages = await redis_client.xreadgroup(
                        self.consumer_group,
                        self.consumer_name,
                        {self.stream_name: ">"},
                        count=self.batch_size,
                        block=self.batch_timeout
                    )
                    
                    if messages:
                        await self._process_messages(messages)
                    
                    # Process any pending messages
                    await self._process_pending_messages()
                    
                    # Process dead letter queue periodically
                    if len(self.dead_letter_queue) > 0:
                        await self._process_dead_letter_queue()
                    
                except RedisConnectionError as e:
                    logger.error(f"Redis connection error in consumer: {e}")
                    await asyncio.sleep(5)  # Wait before retrying
                    
                except Exception as e:
                    logger.error(f"Unexpected error in consumer: {e}")
                    await asyncio.sleep(1)
                    
        except asyncio.CancelledError:
            logger.info("Consumer cancelled")
        finally:
            self.running = False
            logger.info("Consumer stopped")
    
    async def stop_consuming(self) -> None:
        """Stop consuming messages."""
        logger.info("Stopping log consumer")
        self.running = False
    
    async def _process_messages(self, messages: List[Any]) -> None:
        """Process messages from Redis Streams."""
        for stream_name, stream_messages in messages:
            if not stream_messages:
                continue
            
            batch_messages = []
            message_ids = []
            
            for message_id, fields in stream_messages:
                try:
                    # Convert Redis fields to dict
                    message_data = {
                        key.decode('utf-8') if isinstance(key, bytes) else key: 
                        value.decode('utf-8') if isinstance(value, bytes) else value
                        for key, value in fields.items()
                    }
                    
                    batch_messages.append(message_data)
                    message_ids.append(message_id)
                    
                except Exception as e:
                    logger.error(f"Failed to parse message {message_id}: {e}")
                    # Acknowledge failed message to prevent reprocessing
                    await self._acknowledge_message(message_id)
            
            if batch_messages:
                success = await self._process_batch(batch_messages, message_ids)
                if success:
                    # Acknowledge all messages in batch
                    await self._acknowledge_messages(message_ids)
                else:
                    # Add failed messages to dead letter queue
                    await self._handle_failed_batch(batch_messages, message_ids)
    
    async def _process_batch(self, messages: List[Dict[str, Any]], message_ids: List[str]) -> bool:
        """Process a batch of messages."""
        try:
            stats = await log_processor.process_log_batch(messages)
            
            logger.debug(
                f"Processed batch: {len(messages)} messages, "
                f"{stats.messages_processed} successful, "
                f"{stats.messages_failed} failed"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to process message batch: {e}")
            return False
    
    async def _acknowledge_messages(self, message_ids: List[str]) -> None:
        """Acknowledge processed messages."""
        try:
            if message_ids:
                await redis_client.xack(self.stream_name, self.consumer_group, *message_ids)
                logger.debug(f"Acknowledged {len(message_ids)} messages")
        except Exception as e:
            logger.error(f"Failed to acknowledge messages: {e}")
    
    async def _acknowledge_message(self, message_id: str) -> None:
        """Acknowledge a single message."""
        await self._acknowledge_messages([message_id])
    
    async def _handle_failed_batch(self, messages: List[Dict[str, Any]], message_ids: List[str]) -> None:
        """Handle failed batch by adding to dead letter queue."""
        for message, message_id in zip(messages, message_ids):
            self._add_to_dead_letter_queue({
                'message_id': message_id,
                'message_data': message,
                'failed_at': datetime.utcnow().isoformat(),
                'retry_count': 0
            })
        
        logger.warning(f"Added {len(messages)} messages to dead letter queue")
    
    def _add_to_dead_letter_queue(self, failed_message: Dict[str, Any]) -> None:
        """Add message to dead letter queue."""
        if len(self.dead_letter_queue) >= self.max_dlq_size:
            # Remove oldest message
            removed = self.dead_letter_queue.pop(0)
            logger.warning(f"DLQ full, removed oldest message: {removed.get('message_id')}")
        
        self.dead_letter_queue.append(failed_message)
    
    async def _process_dead_letter_queue(self) -> None:
        """Process messages from dead letter queue."""
        if not self.dead_letter_queue:
            return
        
        processed_count = 0
        failed_messages = []
        
        for dlq_message in self.dead_letter_queue[:10]:  # Process up to 10 at a time
            try:
                message_data = dlq_message['message_data']
                success = await log_processor.process_single_message(message_data)
                
                if success:
                    # Acknowledge the original message
                    await self._acknowledge_message(dlq_message['message_id'])
                    processed_count += 1
                else:
                    # Increment retry count
                    dlq_message['retry_count'] += 1
                    if dlq_message['retry_count'] < self.max_retries:
                        failed_messages.append(dlq_message)
                    else:
                        logger.error(f"Message exceeded max retries, dropping: {dlq_message['message_id']}")
                        # Acknowledge to prevent infinite reprocessing
                        await self._acknowledge_message(dlq_message['message_id'])
                        
            except Exception as e:
                logger.error(f"Error processing DLQ message: {e}")
                failed_messages.append(dlq_message)
        
        # Update dead letter queue
        self.dead_letter_queue = failed_messages + self.dead_letter_queue[10:]
        
        if processed_count > 0:
            logger.info(f"Processed {processed_count} messages from dead letter queue")
    
    async def _process_pending_messages(self) -> None:
        """Process pending messages for this consumer."""
        try:
            # Get pending messages for this consumer
            pending_messages = await redis_client.xreadgroup(
                self.consumer_group,
                self.consumer_name,
                {self.stream_name: "0"},  # Read from beginning of pending messages
                count=self.batch_size
            )
            
            if pending_messages:
                await self._process_messages(pending_messages)
                
        except Exception as e:
            logger.error(f"Failed to process pending messages: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get consumer status information."""
        return {
            'running': self.running,
            'consumer_name': self.consumer_name,
            'consumer_group': self.consumer_group,
            'stream_name': self.stream_name,
            'dead_letter_queue_size': len(self.dead_letter_queue),
            'processing_stats': log_processor.get_stats().dict()
        }
    
    def get_dead_letter_queue_size(self) -> int:
        """Get current dead letter queue size."""
        return len(self.dead_letter_queue)


# Global consumer instance
log_consumer = LogConsumerService()