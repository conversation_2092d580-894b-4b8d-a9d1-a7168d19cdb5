"""Unit tests for async log publisher."""

import pytest
from unittest.mock import AsyncMock, patch
from datetime import datetime

from magic_logs.messaging.publisher import AsyncLogPublisher
from magic_logs.core.exceptions import RedisConnectionError


@pytest.fixture
def publisher():
    """Create publisher for testing."""
    return AsyncLogPublisher()


@pytest.mark.asyncio
async def test_publish_log_success(publisher):
    """Test successful log publishing."""
    log_data = {"level": "INFO", "message": "Test message", "user_id": "test_user"}
    
    with patch('magic_logs.messaging.publisher.redis_client') as mock_redis:
        mock_redis.xadd.return_value = "1234567890-0"
        
        await publisher.publish_log(log_data)
        
        mock_redis.xadd.assert_called_once()
        call_args = mock_redis.xadd.call_args
        assert call_args[0][0] == publisher.stream_name  # stream name
        assert "timestamp" in call_args[0][1]  # enriched with timestamp
        assert "service" in call_args[0][1]  # enriched with service
        assert "message_id" in call_args[0][1]  # enriched with message_id


@pytest.mark.asyncio
async def test_publish_log_redis_failure(publisher):
    """Test log publishing with Redis failure."""
    log_data = {"level": "ERROR", "message": "Test error"}
    
    with patch('magic_logs.messaging.publisher.redis_client') as mock_redis:
        mock_redis.xadd.side_effect = RedisConnectionError("Redis unavailable")
        
        await publisher.publish_log(log_data)
        
        # Should buffer locally
        assert len(publisher.local_buffer) == 1
        assert publisher.local_buffer[0]["message"] == "Test error"


@pytest.mark.asyncio
async def test_buffer_overflow(publisher):
    """Test local buffer overflow handling."""
    # Fill buffer beyond capacity
    with patch('magic_logs.messaging.publisher.redis_client') as mock_redis:
        mock_redis.xadd.side_effect = RedisConnectionError("Redis unavailable")
        
        # Add more messages than buffer size
        for i in range(publisher.buffer_size + 10):
            await publisher.publish_log({"message": f"Message {i}"})
        
        # Buffer should not exceed max size
        assert len(publisher.local_buffer) == publisher.buffer_size
        # Should contain the latest messages
        assert publisher.local_buffer[-1]["message"] == f"Message {publisher.buffer_size + 9}"


@pytest.mark.asyncio
async def test_flush_buffer_success(publisher):
    """Test successful buffer flushing."""
    # Add some messages to buffer
    publisher.local_buffer = [
        {"message": "Buffered message 1", "timestamp": datetime.utcnow().isoformat()},
        {"message": "Buffered message 2", "timestamp": datetime.utcnow().isoformat()}
    ]
    
    with patch('magic_logs.messaging.publisher.redis_client') as mock_redis:
        mock_redis.xadd.return_value = "1234567890-0"
        
        flushed_count = await publisher.flush_buffer()
        
        assert flushed_count == 2
        assert len(publisher.local_buffer) == 0
        assert mock_redis.xadd.call_count == 2


@pytest.mark.asyncio
async def test_flush_buffer_partial_failure(publisher):
    """Test buffer flushing with partial failure."""
    # Add some messages to buffer
    publisher.local_buffer = [
        {"message": "Message 1", "timestamp": datetime.utcnow().isoformat()},
        {"message": "Message 2", "timestamp": datetime.utcnow().isoformat()},
        {"message": "Message 3", "timestamp": datetime.utcnow().isoformat()}
    ]
    
    with patch('magic_logs.messaging.publisher.redis_client') as mock_redis:
        # First call succeeds, second fails, third succeeds
        mock_redis.xadd.side_effect = ["1234567890-0", RedisConnectionError("Failed"), "1234567890-2"]
        
        flushed_count = await publisher.flush_buffer()
        
        assert flushed_count == 1  # Only first message flushed
        assert len(publisher.local_buffer) == 2  # Remaining messages still buffered


@pytest.mark.asyncio
async def test_publish_batch_success(publisher):
    """Test successful batch publishing."""
    log_entries = [
        {"level": "INFO", "message": "Message 1"},
        {"level": "WARNING", "message": "Message 2"},
        {"level": "ERROR", "message": "Message 3"}
    ]
    
    with patch('magic_logs.messaging.publisher.redis_client') as mock_redis:
        mock_redis.xadd.return_value = "1234567890-0"
        
        published_count = await publisher.publish_batch(log_entries)
        
        assert published_count == 3
        assert mock_redis.xadd.call_count == 3


@pytest.mark.asyncio
async def test_publish_batch_partial_failure(publisher):
    """Test batch publishing with partial failures."""
    log_entries = [
        {"level": "INFO", "message": "Message 1"},
        {"level": "WARNING", "message": "Message 2"},
        {"level": "ERROR", "message": "Message 3"}
    ]
    
    with patch('magic_logs.messaging.publisher.redis_client') as mock_redis:
        # Second message fails
        mock_redis.xadd.side_effect = ["1234567890-0", RedisConnectionError("Failed"), "1234567890-2"]
        
        published_count = await publisher.publish_batch(log_entries)
        
        assert published_count == 2  # Two successful, one failed
        assert len(publisher.local_buffer) == 1  # Failed message buffered


@pytest.mark.asyncio
async def test_get_buffer_size(publisher):
    """Test buffer size reporting."""
    assert await publisher.get_buffer_size() == 0
    
    # Add some messages to buffer
    publisher.local_buffer = [{"message": "test"}] * 5
    
    assert await publisher.get_buffer_size() == 5