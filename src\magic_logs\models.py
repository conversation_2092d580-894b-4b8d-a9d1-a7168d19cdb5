"""Data models for MagicLogs microservice."""

from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum

from pydantic import BaseModel, Field, validator


class LogLevel(str, Enum):
    """Log level enumeration."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogEntry(BaseModel):
    """Log entry model for validation and processing."""
    
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = None
    correlation_id: Optional[str] = None
    service: str = Field(..., min_length=1, max_length=100)
    level: LogLevel = LogLevel.INFO
    message: str = Field(..., max_length=10000)
    user_id: Optional[str] = Field(None, max_length=255)
    endpoint: Optional[str] = Field(None, max_length=500)
    method: Optional[str] = Field(None, max_length=10)
    status_code: Optional[int] = Field(None, ge=100, le=599)
    processing_time_ms: Optional[int] = Field(None, ge=0)
    error_type: Optional[str] = Field(None, max_length=255)
    error_message: Optional[str] = Field(None, max_length=10000)
    metadata: Optional[Dict[str, Any]] = None
    
    @validator('method')
    def validate_method(cls, v):
        """Validate HTTP method."""
        if v is not None:
            valid_methods = {'GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'}
            if v.upper() not in valid_methods:
                raise ValueError(f'Invalid HTTP method: {v}')
            return v.upper()
        return v
    
    @validator('level', pre=True)
    def validate_level(cls, v):
        """Validate and normalize log level."""
        if isinstance(v, str):
            return v.upper()
        return v
    
    def to_tuple(self) -> tuple:
        """Convert to tuple for database insertion."""
        return (
            self.timestamp,
            self.request_id,
            self.correlation_id,
            self.service,
            self.level.value,
            self.message,
            self.user_id,
            self.endpoint,
            self.method,
            self.status_code,
            self.processing_time_ms,
            self.error_type,
            self.error_message,
            self.metadata
        )
    
    @classmethod
    def get_columns(cls) -> list[str]:
        """Get database column names."""
        return [
            'timestamp', 'request_id', 'correlation_id', 'service', 'level', 'message',
            'user_id', 'endpoint', 'method', 'status_code', 'processing_time_ms',
            'error_type', 'error_message', 'metadata'
        ]


class ProcessingStats(BaseModel):
    """Statistics for log processing."""
    
    messages_processed: int = 0
    messages_failed: int = 0
    messages_skipped: int = 0
    batch_count: int = 0
    processing_time_ms: int = 0
    last_processed_at: Optional[datetime] = None
    
    def add_batch_stats(self, processed: int, failed: int, skipped: int, processing_time: int) -> None:
        """Add statistics from a batch processing operation."""
        self.messages_processed += processed
        self.messages_failed += failed
        self.messages_skipped += skipped
        self.batch_count += 1
        self.processing_time_ms += processing_time
        self.last_processed_at = datetime.utcnow()


class ServiceHealth(BaseModel):
    """Health status model for MagicLogs service."""
    
    status: str = Field(..., description="Service status: healthy, degraded, unhealthy")
    version: str
    uptime_seconds: int
    redis_connected: bool
    database_connected: bool
    consumer_running: bool
    messages_in_buffer: int
    processing_stats: ProcessingStats
    last_error: Optional[str] = None
    
    @property
    def is_healthy(self) -> bool:
        """Check if service is healthy."""
        return (
            self.redis_connected and 
            self.database_connected and 
            self.consumer_running and
            self.status == "healthy"
        )