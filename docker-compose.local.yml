services:
  magic-logs:
    build: .
    container_name: magic-logs-app
    ports:
      - "8002:8002"
    environment:
      # Application settings
      - APP_NAME=MagicLogs
      - APP_VERSION=1.0.0
      - DEBUG=false
      - LOG_LEVEL=INFO
      
      # Server settings
      - HOST=0.0.0.0
      - PORT=8002
      
      # External PostgreSQL settings (rnvaga user)
      - LOGS_DB_HOST=***********
      - LOGS_DB_PORT=5432
      - LOGS_DB_USER=rnvaga
      - LOGS_DB_PASSWORD=!Newupgrade2
      - LOGS_DB_NAME=logs
      - LOGS_DB_SCHEMA=api
      
      # PgBouncer settings (pointing to external DB)
      - PGBOUNCER_HOST=***********
      - PGBOUNCER_PORT=5432
      - PGBOUNCER_POOL_SIZE=25
      - PGBOUNCER_MAX_CLIENT_CONN=100
      
      # Redis settings (container networking)
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - R<PERSON>IS_PASSWORD=
      - REDIS_DB=0
      - REDIS_STREAM_NAME=logs
      - REDIS_CONSUMER_GROUP=magic-logs-group
      
      # Processing settings
      - BATCH_SIZE=1000
      - BATCH_TIMEOUT_SECONDS=5
      - MAX_RETRIES=3
      - PARTITION_RETENTION_MONTHS=12
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/api/v1/health/liveness"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - magic-logs-network

  redis:
    image: redis:7-alpine
    container_name: magic-logs-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - magic-logs-network

volumes:
  redis_data:
    driver: local

networks:
  magic-logs-network:
    driver: bridge
