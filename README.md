# MagicLogs - Centralized Logging Microservice

MagicLogs is a dedicated microservice for handling log ingestion, processing, and storage for the MagicGateway ecosystem. It provides asynchronous log processing through Redis Streams and efficient storage in PostgreSQL with time-based partitioning.

## Features

- **Asynchronous Log Processing**: Uses Redis Streams for non-blocking log ingestion
- **Batch Processing**: Efficient batch processing with configurable batch sizes
- **Time-based Partitioning**: Automatic PostgreSQL table partitioning by month
- **Connection Pooling**: PgBouncer integration for efficient database connections
- **Dead Letter Queue**: Handles failed messages with retry logic
- **Health Monitoring**: Comprehensive health checks and metrics endpoints
- **High Availability**: Graceful error handling and automatic recovery

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│  Redis Streams  │───▶│   MagicLogs     │
│    Services     │    │  (Message Queue)│    │  (Consumer)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │   PostgreSQL    │
                                               │ (Time Partitioned)│
                                               └─────────────────┘
```

## Quick Start

### Using Docker Compose

1. Clone the repository and navigate to the magic-logs directory
2. Copy the environment file:
   ```bash
   cp .env.example .env
   ```
3. Start the services:
   ```bash
   docker-compose up -d
   ```

### Local Development

1. Install dependencies:
   ```bash
   poetry install
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. Start Redis and PostgreSQL (or use Docker):
   ```bash
   docker-compose up -d postgres redis pgbouncer
   ```

4. Run the application:
   ```bash
   poetry run python -m uvicorn src.magic_logs.main:app --reload --port 8002
   ```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `LOGS_DB_HOST` | PostgreSQL host | `localhost` |
| `LOGS_DB_USER` | Database user (write-only) | `logs_writer` |
| `LOGS_DB_PASSWORD` | Database password | - |
| `REDIS_HOST` | Redis host | `localhost` |
| `REDIS_STREAM_NAME` | Redis stream name | `logs` |
| `BATCH_SIZE` | Processing batch size | `1000` |
| `BATCH_TIMEOUT_SECONDS` | Batch timeout | `5` |

### Database Setup

The service automatically creates the required database schema on startup. For manual setup:

```sql
-- Run the initialization script
\i scripts/init-db.sql
```

## API Endpoints

### Health Checks

- `GET /api/v1/health` - Comprehensive health status
- `GET /api/v1/health/liveness` - Kubernetes liveness probe
- `GET /api/v1/health/readiness` - Kubernetes readiness probe
- `GET /api/v1/health/redis` - Redis connection status
- `GET /api/v1/health/database` - Database connection status
- `GET /api/v1/health/consumer` - Consumer status

### Metrics

- `GET /api/v1/metrics` - Service metrics for monitoring

## Usage

### Publishing Logs

Use the `AsyncLogPublisher` from other services:

```python
from magic_logs.messaging.publisher import AsyncLogPublisher

publisher = AsyncLogPublisher()

# Publish a log entry
await publisher.publish_log({
    'service': 'my-service',
    'level': 'INFO',
    'message': 'Operation completed',
    'request_id': 'req_123',
    'user_id': '<EMAIL>'
})
```

### Log Entry Format

```json
{
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456",
    "correlation_id": "corr_789",
    "service": "magic-gateway",
    "level": "INFO",
    "message": "Request processed successfully",
    "user_id": "<EMAIL>",
    "endpoint": "/api/v1/data",
    "method": "GET",
    "status_code": 200,
    "processing_time_ms": 150,
    "error_type": null,
    "error_message": null,
    "metadata": {
        "additional": "context"
    }
}
```

## Monitoring

### Health Status

The service provides detailed health information:

```bash
curl http://localhost:8002/api/v1/health
```

### Metrics

Monitor processing statistics:

```bash
curl http://localhost:8002/api/v1/metrics
```

### Key Metrics

- Messages processed per batch
- Processing time per batch
- Dead letter queue size
- Database connection pool status
- Redis connection status

## Development

### Running Tests

```bash
# Unit tests
poetry run pytest tests/unit/

# Integration tests
poetry run pytest tests/integration/

# All tests with coverage
poetry run pytest --cov=src/magic_logs
```

### Code Quality

```bash
# Format code
poetry run black .
poetry run isort .

# Type checking
poetry run mypy src/
```

## Production Deployment

### Docker

```bash
# Build image
docker build -t magic-logs:latest .

# Run container
docker run -d \
  --name magic-logs \
  -p 8002:8002 \
  -e LOGS_DB_HOST=your-postgres-host \
  -e REDIS_HOST=your-redis-host \
  magic-logs:latest
```

### Kubernetes

Example deployment configuration:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: magic-logs
spec:
  replicas: 2
  selector:
    matchLabels:
      app: magic-logs
  template:
    metadata:
      labels:
        app: magic-logs
    spec:
      containers:
      - name: magic-logs
        image: magic-logs:latest
        ports:
        - containerPort: 8002
        env:
        - name: LOGS_DB_HOST
          value: "postgres-service"
        - name: REDIS_HOST
          value: "redis-service"
        livenessProbe:
          httpGet:
            path: /api/v1/health/liveness
            port: 8002
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/v1/health/readiness
            port: 8002
          initialDelaySeconds: 5
          periodSeconds: 10
```

## Performance Tuning

### Batch Processing

- Increase `BATCH_SIZE` for higher throughput
- Adjust `BATCH_TIMEOUT_SECONDS` for latency vs throughput trade-off

### Database

- Configure PgBouncer pool sizes based on load
- Monitor partition sizes and adjust retention policy
- Use appropriate PostgreSQL configuration for write-heavy workload

### Redis

- Configure Redis memory limits and persistence
- Monitor stream length and consumer lag

## Troubleshooting

### Common Issues

1. **Consumer not processing messages**
   - Check Redis connection
   - Verify consumer group exists
   - Check dead letter queue size

2. **Database connection errors**
   - Verify PgBouncer configuration
   - Check database credentials
   - Monitor connection pool usage

3. **High memory usage**
   - Reduce batch size
   - Check for message accumulation in Redis
   - Monitor dead letter queue

### Logs

The service uses structured JSON logging. Key log events:

- Consumer startup/shutdown
- Batch processing statistics
- Connection failures and recoveries
- Dead letter queue operations

## License

This project is part of the MagicGateway ecosystem.