"""Integration tests for end-to-end log processing."""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import AsyncMock, patch

from magic_logs.processing.consumer import LogConsumerService
from magic_logs.processing.log_processor import LogProcessor
from magic_logs.messaging.publisher import AsyncLogPublisher
from magic_logs.models import LogEntry


@pytest.fixture
async def consumer_service():
    """Create consumer service for testing."""
    service = LogConsumerService()
    # Override with test configuration
    service.consumer_group = "test-group"
    service.stream_name = "test-logs"
    service.batch_size = 10
    service.batch_timeout = 1000
    return service


@pytest.fixture
async def log_processor():
    """Create log processor for testing."""
    return LogProcessor()


@pytest.fixture
async def log_publisher():
    """Create log publisher for testing."""
    publisher = AsyncLogPublisher()
    publisher.stream_name = "test-logs"
    return publisher


@pytest.fixture
def sample_log_messages():
    """Sample log messages for testing."""
    return [
        {
            'timestamp': datetime.utcnow().isoformat(),
            'service': 'test-service',
            'level': 'INFO',
            'message': 'Test message 1',
            'request_id': 'req_001'
        },
        {
            'timestamp': datetime.utcnow().isoformat(),
            'service': 'test-service',
            'level': 'ERROR',
            'message': 'Test error message',
            'request_id': 'req_002',
            'error_type': 'TestError'
        },
        {
            'timestamp': datetime.utcnow().isoformat(),
            'service': 'another-service',
            'level': 'WARNING',
            'message': 'Test warning',
            'user_id': '<EMAIL>'
        }
    ]


@pytest.mark.asyncio
async def test_end_to_end_log_processing(consumer_service, log_processor, sample_log_messages):
    """Test complete log processing workflow."""
    # Mock Redis and database operations
    with patch('magic_logs.processing.consumer.redis_client') as mock_redis, \
         patch('magic_logs.processing.log_processor.log_writer') as mock_writer:
        
        # Setup Redis mock to return our test messages
        mock_redis.xreadgroup.return_value = [
            ["test-logs", [
                ["msg1", {b'service': b'test-service', b'level': b'INFO', b'message': b'Test message 1'}],
                ["msg2", {b'service': b'test-service', b'level': b'ERROR', b'message': b'Test error'}]
            ]]
        ]
        mock_redis.xack.return_value = 2
        mock_redis.xgroup_create.return_value = True
        
        # Setup database writer mock
        mock_writer.write_logs_batch.return_value = 2
        
        # Initialize consumer
        await consumer_service.initialize()
        
        # Process one batch
        messages = await mock_redis.xreadgroup(
            consumer_service.consumer_group,
            consumer_service.consumer_name,
            {consumer_service.stream_name: ">"},
            count=consumer_service.batch_size,
            block=consumer_service.batch_timeout
        )
        
        await consumer_service._process_messages(messages)
        
        # Verify database write was called
        mock_writer.write_logs_batch.assert_called_once()
        
        # Verify messages were acknowledged
        mock_redis.xack.assert_called()


@pytest.mark.asyncio
async def test_log_validation_and_transformation(log_processor, sample_log_messages):
    """Test log validation and transformation."""
    with patch('magic_logs.processing.log_processor.log_writer') as mock_writer:
        mock_writer.write_logs_batch.return_value = len(sample_log_messages)
        
        stats = await log_processor.process_log_batch(sample_log_messages)
        
        assert stats.messages_processed == len(sample_log_messages)
        assert stats.messages_failed == 0
        assert stats.batch_count == 1
        
        # Verify all messages were written
        mock_writer.write_logs_batch.assert_called_once()
        written_logs = mock_writer.write_logs_batch.call_args[0][0]
        assert len(written_logs) == len(sample_log_messages)


@pytest.mark.asyncio
async def test_invalid_message_handling(log_processor):
    """Test handling of invalid log messages."""
    invalid_messages = [
        {'invalid': 'message'},  # Missing required fields
        {'service': '', 'message': 'Empty service'},  # Invalid service
        {'service': 'test', 'level': 'INVALID_LEVEL', 'message': 'Invalid level'},
        {'service': 'test', 'message': 'Valid message'}  # This one should succeed
    ]
    
    with patch('magic_logs.processing.log_processor.log_writer') as mock_writer:
        mock_writer.write_logs_batch.return_value = 1  # Only one valid message
        
        stats = await log_processor.process_log_batch(invalid_messages)
        
        assert stats.messages_processed == 1  # Only one valid message
        assert stats.messages_failed == 3  # Three invalid messages
        
        # Verify only valid message was written
        mock_writer.write_logs_batch.assert_called_once()
        written_logs = mock_writer.write_logs_batch.call_args[0][0]
        assert len(written_logs) == 1


@pytest.mark.asyncio
async def test_dead_letter_queue_processing(consumer_service):
    """Test dead letter queue functionality."""
    with patch('magic_logs.processing.consumer.redis_client') as mock_redis, \
         patch('magic_logs.processing.consumer.log_processor') as mock_processor:
        
        # Setup failed processing
        mock_processor.process_log_batch.side_effect = Exception("Processing failed")
        mock_processor.process_single_message.return_value = True
        
        # Simulate failed batch
        messages = [{'service': 'test', 'message': 'test'}]
        message_ids = ['msg1']
        
        await consumer_service._handle_failed_batch(messages, message_ids)
        
        # Verify message was added to DLQ
        assert len(consumer_service.dead_letter_queue) == 1
        
        # Process DLQ
        await consumer_service._process_dead_letter_queue()
        
        # Verify DLQ message was processed
        mock_processor.process_single_message.assert_called_once()


@pytest.mark.asyncio
async def test_consumer_lifecycle(consumer_service):
    """Test consumer start/stop lifecycle."""
    with patch('magic_logs.processing.consumer.redis_client') as mock_redis:
        mock_redis.xgroup_create.return_value = True
        mock_redis.xreadgroup.return_value = []  # No messages
        
        # Initialize consumer
        await consumer_service.initialize()
        
        # Start consumer in background
        consumer_task = asyncio.create_task(consumer_service.start_consuming())
        
        # Let it run briefly
        await asyncio.sleep(0.1)
        
        # Stop consumer
        await consumer_service.stop_consuming()
        
        # Wait for task to complete
        await asyncio.wait_for(consumer_task, timeout=1.0)
        
        assert not consumer_service.running


@pytest.mark.asyncio
async def test_batch_processing_performance(log_processor):
    """Test batch processing performance with large batches."""
    # Create large batch of messages
    large_batch = []
    for i in range(1000):
        large_batch.append({
            'timestamp': datetime.utcnow().isoformat(),
            'service': f'service-{i % 10}',
            'level': 'INFO',
            'message': f'Message {i}',
            'request_id': f'req_{i}'
        })
    
    with patch('magic_logs.processing.log_processor.log_writer') as mock_writer:
        mock_writer.write_logs_batch.return_value = len(large_batch)
        
        stats = await log_processor.process_log_batch(large_batch)
        
        assert stats.messages_processed == len(large_batch)
        assert stats.messages_failed == 0
        assert stats.processing_time_ms > 0
        
        # Verify batch was written efficiently
        mock_writer.write_logs_batch.assert_called_once()


@pytest.mark.asyncio
async def test_redis_connection_failure_handling(consumer_service):
    """Test handling of Redis connection failures."""
    with patch('magic_logs.processing.consumer.redis_client') as mock_redis:
        mock_redis.xgroup_create.return_value = True
        mock_redis.xreadgroup.side_effect = Exception("Redis connection failed")
        
        # Initialize consumer
        await consumer_service.initialize()
        
        # Start consumer (should handle connection failures gracefully)
        consumer_task = asyncio.create_task(consumer_service.start_consuming())
        
        # Let it run briefly to encounter the error
        await asyncio.sleep(0.1)
        
        # Stop consumer
        await consumer_service.stop_consuming()
        
        # Wait for task to complete
        await asyncio.wait_for(consumer_task, timeout=2.0)
        
        # Consumer should have handled the error and stopped gracefully
        assert not consumer_service.running


@pytest.mark.asyncio
async def test_message_acknowledgment(consumer_service):
    """Test proper message acknowledgment."""
    with patch('magic_logs.processing.consumer.redis_client') as mock_redis, \
         patch('magic_logs.processing.consumer.log_processor') as mock_processor:
        
        mock_redis.xgroup_create.return_value = True
        mock_processor.process_log_batch.return_value = AsyncMock()
        mock_processor.process_log_batch.return_value.messages_processed = 2
        
        # Simulate successful processing
        messages = [
            ["test-logs", [
                ["msg1", {b'service': b'test', b'message': b'test1'}],
                ["msg2", {b'service': b'test', b'message': b'test2'}]
            ]]
        ]
        
        await consumer_service._process_messages(messages)
        
        # Verify messages were acknowledged
        mock_redis.xack.assert_called_once_with(
            consumer_service.stream_name,
            consumer_service.consumer_group,
            "msg1", "msg2"
        )