"""Unit tests for PostgreSQL connection manager."""

import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import asyncpg

from magic_logs.db.connection_manager import PostgreSQLConnectionManager
from magic_logs.core.exceptions import DatabaseConnectionError


@pytest.fixture
def connection_manager():
    """Create connection manager for testing."""
    return PostgreSQLConnectionManager()


@pytest.mark.asyncio
async def test_initialize_success(connection_manager):
    """Test successful connection pool initialization."""
    with patch('asyncpg.create_pool') as mock_create_pool:
        mock_pool = AsyncMock()
        mock_create_pool.return_value = mock_pool
        
        # Mock connection for test query
        mock_conn = AsyncMock()
        mock_conn.fetchval.return_value = 1
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        
        await connection_manager.initialize()
        
        assert connection_manager._pool is not None
        mock_create_pool.assert_called_once()
        mock_conn.fetchval.assert_called_once_with('SELECT 1')


@pytest.mark.asyncio
async def test_initialize_failure(connection_manager):
    """Test connection pool initialization failure."""
    with patch('asyncpg.create_pool') as mock_create_pool:
        mock_create_pool.side_effect = asyncpg.PostgresError("Connection failed")
        
        with pytest.raises(DatabaseConnectionError):
            await connection_manager.initialize()


@pytest.mark.asyncio
async def test_get_connection_success(connection_manager):
    """Test successful connection acquisition."""
    mock_pool = AsyncMock()
    mock_conn = AsyncMock()
    mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
    
    connection_manager._pool = mock_pool
    
    async with connection_manager.get_connection() as conn:
        assert conn is mock_conn


@pytest.mark.asyncio
async def test_get_connection_with_retry(connection_manager):
    """Test connection acquisition with retry logic."""
    mock_pool = AsyncMock()
    mock_conn = AsyncMock()
    
    # First attempt fails, second succeeds
    mock_pool.acquire.side_effect = [
        asyncpg.InterfaceError("Connection lost"),
        AsyncMock().__aenter__.return_value
    ]
    mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
    
    connection_manager._pool = mock_pool
    
    with patch('asyncio.sleep'):  # Mock sleep to speed up test
        async with connection_manager.get_connection() as conn:
            pass  # Should succeed on retry


@pytest.mark.asyncio
async def test_execute_query_success(connection_manager):
    """Test successful query execution."""
    mock_conn = AsyncMock()
    mock_conn.execute.return_value = "INSERT 0 1"
    
    with patch.object(connection_manager, 'get_connection') as mock_get_conn:
        mock_get_conn.return_value.__aenter__.return_value = mock_conn
        
        result = await connection_manager.execute_query("INSERT INTO test VALUES ($1)", "value")
        
        assert result == "INSERT 0 1"
        mock_conn.execute.assert_called_once_with("INSERT INTO test VALUES ($1)", "value")


@pytest.mark.asyncio
async def test_fetch_one_success(connection_manager):
    """Test successful single row fetch."""
    mock_conn = AsyncMock()
    mock_row = MagicMock()
    mock_row.__iter__.return_value = iter([('id', 1), ('name', 'test')])
    mock_conn.fetchrow.return_value = mock_row
    
    with patch.object(connection_manager, 'get_connection') as mock_get_conn:
        mock_get_conn.return_value.__aenter__.return_value = mock_conn
        
        result = await connection_manager.fetch_one("SELECT * FROM test WHERE id = $1", 1)
        
        assert result == {'id': 1, 'name': 'test'}
        mock_conn.fetchrow.assert_called_once_with("SELECT * FROM test WHERE id = $1", 1)


@pytest.mark.asyncio
async def test_fetch_one_no_result(connection_manager):
    """Test fetch_one with no results."""
    mock_conn = AsyncMock()
    mock_conn.fetchrow.return_value = None
    
    with patch.object(connection_manager, 'get_connection') as mock_get_conn:
        mock_get_conn.return_value.__aenter__.return_value = mock_conn
        
        result = await connection_manager.fetch_one("SELECT * FROM test WHERE id = $1", 999)
        
        assert result is None


@pytest.mark.asyncio
async def test_fetch_all_success(connection_manager):
    """Test successful multiple rows fetch."""
    mock_conn = AsyncMock()
    mock_rows = [
        MagicMock(__iter__=lambda self: iter([('id', 1), ('name', 'test1')])),
        MagicMock(__iter__=lambda self: iter([('id', 2), ('name', 'test2')]))
    ]
    mock_conn.fetch.return_value = mock_rows
    
    with patch.object(connection_manager, 'get_connection') as mock_get_conn:
        mock_get_conn.return_value.__aenter__.return_value = mock_conn
        
        result = await connection_manager.fetch_all("SELECT * FROM test")
        
        assert len(result) == 2
        assert result[0] == {'id': 1, 'name': 'test1'}
        assert result[1] == {'id': 2, 'name': 'test2'}


@pytest.mark.asyncio
async def test_health_check_success(connection_manager):
    """Test successful health check."""
    mock_conn = AsyncMock()
    mock_conn.fetchval.return_value = 1
    
    with patch.object(connection_manager, 'get_connection') as mock_get_conn:
        mock_get_conn.return_value.__aenter__.return_value = mock_conn
        
        result = await connection_manager.health_check()
        
        assert result is True
        mock_conn.fetchval.assert_called_once_with('SELECT 1')


@pytest.mark.asyncio
async def test_health_check_failure(connection_manager):
    """Test health check failure."""
    with patch.object(connection_manager, 'get_connection') as mock_get_conn:
        mock_get_conn.side_effect = DatabaseConnectionError("Connection failed")
        
        result = await connection_manager.health_check()
        
        assert result is False


@pytest.mark.asyncio
async def test_get_pool_stats_initialized(connection_manager):
    """Test pool stats when pool is initialized."""
    mock_pool = AsyncMock()
    mock_pool.get_size.return_value = 10
    mock_pool.get_min_size.return_value = 5
    mock_pool.get_max_size.return_value = 25
    mock_pool.get_idle_size.return_value = 3
    
    connection_manager._pool = mock_pool
    
    with patch.object(connection_manager, 'health_check', return_value=True):
        stats = await connection_manager.get_pool_stats()
        
        assert stats['size'] == 10
        assert stats['min_size'] == 5
        assert stats['max_size'] == 25
        assert stats['idle_size'] == 3
        assert stats['status'] == 'healthy'


@pytest.mark.asyncio
async def test_get_pool_stats_not_initialized(connection_manager):
    """Test pool stats when pool is not initialized."""
    stats = await connection_manager.get_pool_stats()
    
    assert stats == {"status": "not_initialized"}


@pytest.mark.asyncio
async def test_close(connection_manager):
    """Test connection pool cleanup."""
    mock_pool = AsyncMock()
    connection_manager._pool = mock_pool
    
    await connection_manager.close()
    
    mock_pool.close.assert_called_once()