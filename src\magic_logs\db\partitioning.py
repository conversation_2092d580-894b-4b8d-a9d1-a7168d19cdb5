"""Time-based partitioning logic for logs table."""

from datetime import datetime, date, timedelta
from typing import List, Optional
import calendar

from loguru import logger

from .connection_manager import db_manager
from ..core.config import settings


class LogTablePartitionManager:
    """Manages time-based partitioning for logs table."""
    
    def __init__(self) -> None:
        self.retention_months = settings.PARTITION_RETENTION_MONTHS
    
    async def ensure_partition_exists(self, target_date: date) -> bool:
        """Ensure partition exists for the given date."""
        partition_name = self._get_partition_name(target_date)
        
        if await self._partition_exists(partition_name):
            return True
        
        return await self._create_partition(target_date, partition_name)
    
    async def create_base_table_if_not_exists(self) -> bool:
        """Create the base partitioned logs table if it doesn't exist."""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS request_logs (
            id BIGSERIAL,
            timestamp TIMESTAMPTZ NOT NULL,
            request_id VARCHAR(255),
            correlation_id VARCHAR(255),
            service VARCHAR(100) NOT NULL,
            level VARCHAR(20) NOT NULL,
            message TEXT,
            user_id VARCHAR(255),
            endpoint VARCHAR(500),
            method VARCHAR(10),
            status_code INTEGER,
            processing_time_ms INTEGER,
            error_type VARCHAR(255),
            error_message TEXT,
            metadata JSONB,
            created_at TIMESTAMPTZ DEFAULT NOW()
        ) PARTITION BY RANGE (timestamp);
        
        CREATE INDEX IF NOT EXISTS idx_request_logs_timestamp ON request_logs (timestamp);
        CREATE INDEX IF NOT EXISTS idx_request_logs_request_id ON request_logs (request_id);
        CREATE INDEX IF NOT EXISTS idx_request_logs_service ON request_logs (service);
        CREATE INDEX IF NOT EXISTS idx_request_logs_level ON request_logs (level);
        CREATE INDEX IF NOT EXISTS idx_request_logs_user_id ON request_logs (user_id);
        """
        
        try:
            async with db_manager.get_connection() as conn:
                await conn.execute(create_table_sql)
            logger.info("Base logs table created/verified successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to create base logs table: {e}")
            return False
    
    def _get_partition_name(self, target_date: date) -> str:
        """Generate partition name for the given date."""
        return f"request_logs_{target_date.strftime('%Y_%m')}"
    
    def _get_partition_bounds(self, target_date: date) -> tuple[date, date]:
        """Get partition bounds for the given date."""
        start_date = target_date.replace(day=1)
        
        # Get last day of the month
        last_day = calendar.monthrange(target_date.year, target_date.month)[1]
        end_date = start_date.replace(day=last_day) + timedelta(days=1)
        
        return start_date, end_date
    
    async def _partition_exists(self, partition_name: str) -> bool:
        """Check if partition already exists."""
        check_sql = """
        SELECT EXISTS (
            SELECT 1 FROM pg_tables 
            WHERE schemaname = $1 AND tablename = $2
        )
        """
        
        try:
            async with db_manager.get_connection() as conn:
                exists = await conn.fetchval(check_sql, settings.LOGS_DB_SCHEMA, partition_name)
                return bool(exists)
        except Exception as e:
            logger.error(f"Failed to check partition existence: {e}")
            return False
    
    async def _create_partition(self, target_date: date, partition_name: str) -> bool:
        """Create partition for the given date."""
        start_date, end_date = self._get_partition_bounds(target_date)
        
        create_partition_sql = f"""
        CREATE TABLE {partition_name} PARTITION OF request_logs
        FOR VALUES FROM ('{start_date}') TO ('{end_date}');
        
        CREATE INDEX idx_{partition_name}_timestamp ON {partition_name} (timestamp);
        CREATE INDEX idx_{partition_name}_request_id ON {partition_name} (request_id);
        CREATE INDEX idx_{partition_name}_service ON {partition_name} (service);
        """
        
        try:
            async with db_manager.get_connection() as conn:
                await conn.execute(create_partition_sql)
            logger.info(f"Created partition {partition_name} for period {start_date} to {end_date}")
            return True
        except Exception as e:
            logger.error(f"Failed to create partition {partition_name}: {e}")
            return False
    
    async def cleanup_old_partitions(self) -> int:
        """Remove partitions older than retention period."""
        cutoff_date = datetime.now().date() - timedelta(days=self.retention_months * 30)
        
        # Get list of old partitions
        list_partitions_sql = """
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE schemaname = $1 
        AND tablename LIKE 'request_logs_%'
        AND tablename ~ '^request_logs_[0-9]{4}_[0-9]{2}$'
        """
        
        try:
            async with db_manager.get_connection() as conn:
                partitions = await conn.fetch(list_partitions_sql, settings.LOGS_DB_SCHEMA)
            
            dropped_count = 0
            for partition in partitions:
                partition_name = partition['tablename']
                
                # Extract date from partition name
                try:
                    date_part = partition_name.replace('request_logs_', '')
                    year, month = map(int, date_part.split('_'))
                    partition_date = date(year, month, 1)
                    
                    if partition_date < cutoff_date:
                        if await self._drop_partition(partition_name):
                            dropped_count += 1
                            
                except ValueError:
                    logger.warning(f"Could not parse date from partition name: {partition_name}")
                    continue
            
            logger.info(f"Cleaned up {dropped_count} old partitions")
            return dropped_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old partitions: {e}")
            return 0
    
    async def _drop_partition(self, partition_name: str) -> bool:
        """Drop a specific partition."""
        drop_sql = f"DROP TABLE IF EXISTS {partition_name}"
        
        try:
            async with db_manager.get_connection() as conn:
                await conn.execute(drop_sql)
            logger.info(f"Dropped old partition: {partition_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to drop partition {partition_name}: {e}")
            return False
    
    async def get_partition_info(self) -> List[dict]:
        """Get information about existing partitions."""
        info_sql = """
        SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
            (SELECT count(*) FROM information_schema.tables t2 
             WHERE t2.table_schema = t.schemaname 
             AND t2.table_name = t.tablename) as exists
        FROM pg_tables t
        WHERE schemaname = $1 
        AND tablename LIKE 'request_logs_%'
        ORDER BY tablename
        """
        
        try:
            async with db_manager.get_connection() as conn:
                partitions = await conn.fetch(info_sql, settings.LOGS_DB_SCHEMA)
                return [dict(partition) for partition in partitions]
        except Exception as e:
            logger.error(f"Failed to get partition info: {e}")
            return []


# Global partition manager instance
partition_manager = LogTablePartitionManager()